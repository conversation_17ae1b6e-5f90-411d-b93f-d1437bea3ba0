package com.bestpay.bigdata.bi.parse.tools;

import com.bestpay.bigdata.bi.common.exception.ParseSqlException;
import org.antlr.v4.runtime.BaseErrorListener;
import org.antlr.v4.runtime.RecognitionException;
import org.antlr.v4.runtime.Recognizer;


public class SyntaxErrorListener extends BaseErrorListener {

    @Override
    public void syntaxError(Recognizer<?, ?> recognizer, Object offendingSymbol, int line, int charPositionInLine, String msg, RecognitionException e) {
        msg = "sql语法解析异常：" + "line " + line + ":" + charPositionInLine + " " + msg;
        throw new ParseSqlException(msg);
    }
}
