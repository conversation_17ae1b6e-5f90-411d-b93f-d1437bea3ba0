package com.bestpay.bigdata.bi.analysis.Util;

import com.bestpay.bigdata.bi.analysis.bean.jsonanalysis.Dataset;
import com.bestpay.bigdata.bi.common.common.Constant;
import com.bestpay.bigdata.bi.common.enums.CodeEnum;
import com.bestpay.bigdata.bi.common.exception.JsonCheckException;
import com.bestpay.bigdata.bi.common.util.CollectionUtil;
import java.util.List;

/**
 * 数据集信息检验
 *
 * <AUTHOR>
 * @date 2021/12/10
 */

public class DataSetCheck {

  public static void check(Dataset dataset){
    List<String> stringList=dataset.getTables();
    if (CollectionUtil.isEmpty(stringList)) {
      throw new JsonCheckException(CodeEnum.JSON_CHECK_FORMAT_ERROR.code(),"数据集中缺少表内容！");
    }
    for(String str:stringList){
      String[] arr=str.split(Constant.JSON_CHECK_DATASET_TABLE_SPLIT);
      if(arr.length != Constant.JSON_CHECK_DATASET_TABLE_SPLIT_LENGTH)
      {
        throw new JsonCheckException(CodeEnum.JSON_CHECK_FORMAT_ERROR.code(),"数据集中表内容格式错误！"+str);
      }
    }
  }
}
