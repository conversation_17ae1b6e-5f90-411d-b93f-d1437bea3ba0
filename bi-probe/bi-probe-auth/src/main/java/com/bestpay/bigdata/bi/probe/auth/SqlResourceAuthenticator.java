package com.bestpay.bigdata.bi.probe.auth;

/**
 * <AUTHOR>
 * @Date 2022/9/1
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.bestpay.bigdata.bi.common.common.Constant;
import com.bestpay.bigdata.bi.common.dto.metadata.MetaDataUserPermission;
import com.bestpay.bigdata.bi.common.enums.CodeEnum;
import com.bestpay.bigdata.bi.common.enums.SQLEngine;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import com.bestpay.bigdata.bi.common.exception.ParameterException;
import com.bestpay.bigdata.bi.parse.entity.ResourceInfo;
import com.bestpay.bigdata.bi.parse.enums.RelationType;
import com.bestpay.bigdata.bi.parse.enums.StatementType;
import com.bestpay.bigdata.bi.probe.enums.SqlPermissionOperatorEnum;
import com.bestpay.bigdata.bi.thirdparty.api.MetaDataService;
import java.text.MessageFormat;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Slf4j
@Component
public class SqlResourceAuthenticator {

  @Value("${hive.temp.schemaName:tmp_bi_csv}")
  private String tempSchemaName;

  @Resource
  private MetaDataService metaDataService;

  public void authenticate(Authentication authentication) {
    String sqlEngine = authentication.getSqlEngine();
    if (SQLEngine.KYUUBI.getEngineName().equals(sqlEngine) ||
            SQLEngine.HIVE.getEngineName().equals(sqlEngine)) {
      syntaxCheck(authentication);
    }
    metadataPermissionCheck(authentication);
  }



  public void metadataPermissionCheck(Authentication authentication) {

    List<ResourceInfo> resources = authentication.getResources();
    String datasource = authentication.getDatasource();
    String username = authentication.getUsername();

    Map<String, List<ResourceInfo>> resourceMap = resources.stream()
        .collect(Collectors.groupingBy(this::convertStatementType));
    List<MetaDataUserPermission> userPermissions = getMetaDataUserPermissions(username, datasource, resourceMap);
    userPermissionsCheck(userPermissions);
  }

  public void syntaxCheck(Authentication authentication) {
    for (ResourceInfo resource : authentication.getResources()) {
      udfPermissionCheck(resource, authentication.getEngineSupportUdf(), authentication.getAllowUdfInComplexSql());
      SqlPermissionOperatorEnum operator = SqlPermissionOperatorEnum.convertStatementType(resource.getStatementType());
      if (!operator.isAccessible(resource.getDbName())) {
        CodeEnum errorMessageEnum = operator.getErrorMessageEnum();
        if (CodeEnum.SQL_SYNATAX_OTHER_CHECK_ERROR.equals(errorMessageEnum)) {
          throw new ParameterException(MessageFormat.format(errorMessageEnum.message(), operator.getEnumName()),
              errorMessageEnum.code());
        }

        throw new ParameterException(errorMessageEnum.message(), errorMessageEnum.code());
      }

    }

    // only allow create table in tmp_bi_csv database
    if (Objects.equals(SQLEngine.HIVE.getEngineName().toLowerCase(), authentication.getSqlEngine().toLowerCase()) ||
            Objects.equals(SQLEngine.PRESTO.getEngineName().toLowerCase(), authentication.getSqlEngine().toLowerCase())) {
      isAllowCreateTableAs(authentication.getResources());
    }
  }

  private void isAllowCreateTableAs(List<ResourceInfo> resourceInfos) {
    if(CollectionUtils.isEmpty(resourceInfos)) {
      return;
    }

    Optional<ResourceInfo> createTableAsResourceOptional = resourceInfos.stream()
            .filter(res -> res.getStatementType().equals(StatementType.CREATE_TABLE_AS.name())).findFirst();
    if (!createTableAsResourceOptional.isPresent()) {
      return;
    }

    Optional<ResourceInfo> selectResourceOptional = resourceInfos.stream()
            .filter(res -> StatementType.SELECT.name().equals(res.getStatementType())
                    && res.getDbName().equals(tempSchemaName)).findFirst();
    ResourceInfo resourceInfo = createTableAsResourceOptional.get();
    if (selectResourceOptional.isPresent()&&!tempSchemaName.equals(resourceInfo.getDbName())) {
      log.info("create table as tempSchema.table scope only in tempSchema:{}",tempSchemaName);
      throw new BusinessException(CodeEnum.CREATE_TABLE_AS_TEMP_SCHEMA_ERROR);
    }
  }

  public void udfPermissionCheck(ResourceInfo resourceInfo, Boolean engineSupportUdf, Boolean allowUdfInComplexSql) {

    if (Objects.equals(StatementType.USE_UDF.name(), resourceInfo.getStatementType())) {
      if (!engineSupportUdf) {
        throw new ParameterException(CodeEnum.SQL_UDF_FUNC_HIVE_ENGINE_ERROR);
      }

      if (Objects.equals(RelationType.JOIN.name(), resourceInfo.getRelationType().name()) && !allowUdfInComplexSql) {
        throw new ParameterException(CodeEnum.SQL_UDF_FUNC_UNION_TABLE_ERROR);
      }
    }
  }

  private String convertStatementType(ResourceInfo e) {
    if (Objects.equals(StatementType.USE_UDF.name(), e.getStatementType())) {
      return e.getFunctionName();
    }
    SqlPermissionOperatorEnum sqlPermissionOperatorEnum = SqlPermissionOperatorEnum.convertStatementType(
        e.getStatementType());
    return sqlPermissionOperatorEnum.getPermission().toLowerCase();
  }

  private List<MetaDataUserPermission> getMetaDataUserPermissions(String username, String databaseSource,
      Map<String, List<ResourceInfo>> resourceMap) {

    return resourceMap.entrySet().stream().map(entry -> {
      String permissionItem = entry.getKey();
      List<ResourceInfo> resourceInfos = entry.getValue();
      MetaDataUserPermission userPermission = new MetaDataUserPermission();
      userPermission.setUsername(username);
      userPermission.setPermission(permissionItem);
      userPermission.setPlatform("BI");
      if (!CollectionUtils.isEmpty(resourceInfos)) {
        List<String> permissionResources = resourceInfos.stream().
            map(res -> getPermissionResource(res, databaseSource)).collect(Collectors.toList());
        userPermission.setResources(permissionResources);
      }
      return userPermission;
    }).collect(Collectors.toList());
  }

  private void userPermissionsCheck(List<MetaDataUserPermission> userPermissions) {
    for (MetaDataUserPermission userPermission : userPermissions) {
      if (Constant.SQL_PERMISSION_SHOW.equalsIgnoreCase(userPermission.getPermission()) ||
              Constant.SQL_PERMISSION_SET.equalsIgnoreCase(userPermission.getPermission())) {
        continue;
      }

      List<String> failedResourcesLists = metaDataService.metaDataUserPermissionCheck(userPermission);
      if (CollUtil.isNotEmpty(failedResourcesLists)) {
        //判断是否是UDF函数
        if (userPermission.getPermission().contains(StrUtil.DOT)) {
          throw new ParameterException(StrUtil.format(CodeEnum.SQL_UDF_FUNC_CHECK_PERMISSION_ERROR.message(),
              userPermission.getPermission(), userPermission.getResources()),
              CodeEnum.SQL_UDF_FUNC_CHECK_PERMISSION_ERROR.code());
        }
        throw new ParameterException(
            "数据权限校验失败！用户没有" + userPermission.getPermission() + "  " + failedResourcesLists + "的权限。",
            CodeEnum.USER_PERMISSION_CHECK_ERROR.code());
      }
    }
  }

  private String getPermissionResource(ResourceInfo resourceInfo, String databaseSource) {
    String slash = Constant.SEPARATORS_CHAR;
    String tableName = resourceInfo.getTable();
    String databaseLevel = slash + databaseSource + slash + resourceInfo.getDbName();
    return StringUtils.isNotBlank(tableName) ? databaseLevel + slash + tableName : databaseLevel;
  }

  public int parseSqlType(List<ResourceInfo> resources) {
    for (ResourceInfo resource : resources) {
      SqlPermissionOperatorEnum sqlPermissionOperatorEnum = SqlPermissionOperatorEnum.convertStatementType(
          resource.getStatementType());
      String permission = sqlPermissionOperatorEnum.getPermission();
      if (permission.equalsIgnoreCase(Constant.SQL_PERMISSION_INSERT) || permission.equalsIgnoreCase(
          Constant.SQL_PERMISSION_ALTER) || permission.equalsIgnoreCase(Constant.SQL_PERMISSION_CREATE)
          || permission.equalsIgnoreCase(Constant.SQL_PERMISSION_DELETE) || permission.equalsIgnoreCase(
          Constant.SQL_PERMISSION_DROP) || permission.equalsIgnoreCase(Constant.SQL_PERMISSION_TRUNCATE)
          || permission.equalsIgnoreCase(Constant.SQL_PERMISSION_SET)) {
        return Constant.SQL_NONE_DQL_TYPE;
      }
    }

    return Constant.SQL_DQL_TYPE;
  }
}
