server:
  port: 8090
app:
  id:
    bigdata-bi
apollo:
  bootstrap:
    enabled: true
    eagerLoad:
      enabled: true
    namespaces: application,auth-config.yml,dubbo,logging,redis.yml,mysql,dataSource,dynamic_refresh

dubbo:
  application:
    name: bi-probe-openapi
  protocol:
    port: 21996
  registry:
    simplified: true

logging:
  config: classpath:logback-probe-openapi.xml
spring:
  application:
    name: bi-probe-openapi
  servlet:
    multipart:
      enabled: true
      max-file-size: 931775308
      max-request-size: 1000MB