package com.bestpay.bigdata.bi.database.bean.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 报表列表响应类
 *
 * <AUTHOR>
 * @since 2022-02-16 11:06:32
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@ApiModel("报表列表响应类")
public class ReportResponse extends Report{

  /**
   * 归属组织名称
   */
  @ApiModelProperty(value = "归属组织名称")
  private String orgName;
  /**
   * 归属目录名称
   */
  @ApiModelProperty(value = "归属目录名称")
  private String dirName;

  /**
   * 报表访问量
   */
  @ApiModelProperty(value = "报表访问量")
  private Long userAccessCount;
}
