package com.bestpay.bigdata.bi.database.mapper.dataset;

import com.bestpay.bigdata.bi.common.dto.dataset.DatasetElementQueryDTO;
import com.bestpay.bigdata.bi.database.dao.dataset.DatasetElementDo;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023-07-05-9:49
 */
public interface DatasetElementMapper {

    List<DatasetElementDo> query(DatasetElementQueryDTO queryDTO);

    int batchInsert(List<DatasetElementDo> list);

    int update(DatasetElementDo elementDo);
}
