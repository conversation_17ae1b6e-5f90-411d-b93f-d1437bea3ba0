package com.bestpay.bigdata.bi.database.dao.quartz;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QrtzTriggersDo implements Serializable {
  private static final long serialVersionUID = 1L;

  private String schedName;
  private String triggerName;
  private String triggerGroup;
  private String jobName;
  private String jobGroup;
  private String description;
  private Long nextFireTime;
  private Long prevFireTime;
  private Integer priority;
  private String triggerState;
  private String triggerType;
  private Long startTime;
  private Long endTime;
  private String calendarName;
  private Long misfireInstr;
  private String jobData;

}
