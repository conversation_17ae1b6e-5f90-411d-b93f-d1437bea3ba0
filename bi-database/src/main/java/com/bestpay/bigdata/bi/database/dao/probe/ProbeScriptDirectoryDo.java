package com.bestpay.bigdata.bi.database.dao.probe;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ClassName: ProbeScriptDirectoryDo
 * Package: com.bestpay.bigdata.bi.database.dao
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/9/18 17:20
 * @Version 1.0
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProbeScriptDirectoryDo implements Serializable {

    private static final long serialVersionUID = -19310982607151310L;

    private Long id;

    /**
     * 组织code
     */
    private String orgCode;

    /**
     * 目录名称
     */
    private String name;

    /**
     * 目录顺序
     */
    private Long dirSort;


    /**
     * 状态（0：正常   9：删除）
     */
    private Integer statusCode;

    private Date createdAt;

    private String createdBy;

    private Date updatedAt;

    private String updatedBy;
}
