package com.bestpay.bigdata.bi.database.mapper.dashboard;

import com.bestpay.bigdata.bi.common.dto.dashboard.IndexCardQueryDTO;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardIndexTextCardDO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface DashboardIndexTextCardMapper {

  Long insert(DashboardIndexTextCardDO cardDO);

  List<DashboardIndexTextCardDO> find(IndexCardQueryDTO cardQuery);

  void update(DashboardIndexTextCardDO cardDO);

  void batchInsert(
      @Param("dashboardIndexTextCardDOList") List<DashboardIndexTextCardDO> dashboardIndexTextCardDOList);


  List<DashboardIndexTextCardDO> queryByDashboardIdList(
      @Param("dashboardIdList") List<Long> dashboardIdList,
      @Param("indexCardType") String indexCardType);

  // 新增备份表操作方法
  List<DashboardIndexTextCardDO> findBak(IndexCardQueryDTO cardQuery);

  void batchUpdateBak(@Param("indexTextCardList") List<DashboardIndexTextCardDO> indexTextCardList);
}
