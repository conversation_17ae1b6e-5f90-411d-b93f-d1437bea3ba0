package com.bestpay.bigdata.bi.database.api.dataset;

import com.bestpay.bigdata.bi.common.dto.dataset.DatasetParamQueryDTO;
import com.bestpay.bigdata.bi.database.dao.dataset.DatasetParamDo;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface DatasetParamDAOService {

    List<DatasetParamDo> query(DatasetParamQueryDTO queryDTO);

    int batchInsert(List<DatasetParamDo> list);

    int update(DatasetParamDo paramDo);

    int updateById(DatasetParamDo paramDo);
}
