package com.bestpay.bigdata.bi.database.mapper.common;

import com.bestpay.bigdata.bi.database.bean.report.Report;
import com.bestpay.bigdata.bi.database.bean.report.ReportQueryDTO;
import java.util.List;

/**
 * 报表(Report)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-02-16 11:06:32
 */

public interface ReportMapper {

  /**
   * 通过ID查询单条数据
   *
   * @param id 主键
   * @return 实例对象
   */
  Report queryById(Long id);

  int update(Report report);

  List<Report> queryAll(ReportQueryDTO report);
}

