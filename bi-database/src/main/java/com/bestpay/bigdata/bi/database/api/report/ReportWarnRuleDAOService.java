package com.bestpay.bigdata.bi.database.api.report;

import com.bestpay.bigdata.bi.common.dto.warn.ReportWarnRuleDTO;
import com.bestpay.bigdata.bi.database.dao.warn.ReportWarnRuleDo;
import java.util.List;

/**
 * ClassName: ReportWarnRuleDAOService
 * Package: com.bestpay.bigdata.bi.database.api
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/8/4 14:44
 * @Version 1.0
 */
public interface ReportWarnRuleDAOService {

    List<ReportWarnRuleDo> query(ReportWarnRuleDTO reportWarnRuleDTO);

    int update(ReportWarnRuleDo ruleDo);

    int updateById(ReportWarnRuleDo ruleDo);

    int batchInsert(List<ReportWarnRuleDo> reportWarnRuleDo);

    // 新增备份表操作方法
    List<ReportWarnRuleDo> queryBak(ReportWarnRuleDTO reportWarnRuleDTO);
}
