package com.bestpay.bigdata.bi.database.mapper.datascreen;

import com.bestpay.bigdata.bi.database.dao.datascreen.AxisAndChartFieldDO;
import com.bestpay.bigdata.bi.database.dao.datascreen.ReportComponentDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-08-29
 */
public interface ReportComponentMapper {
    Long insert(ReportComponentDO reportComponentDO);

    Long insertShade(ReportComponentDO reportComponentDO);

    ReportComponentDO findById(Long id);

    List<ReportComponentDO> findAll();

    List<ReportComponentDO> findShadeAll();

    Long update(ReportComponentDO reportComponentDO);

    List<ReportComponentDO> queryByIdListAndDataSetId(@Param("dataScreenReportCardIdList") List<Long> dataScreenReportCardIdList, @Param("datasetId") Long datasetId);

    List<AxisAndChartFieldDO> queryByChartTypeList(@Param("chartTypeList") List<Integer> chartTypeList);
}
