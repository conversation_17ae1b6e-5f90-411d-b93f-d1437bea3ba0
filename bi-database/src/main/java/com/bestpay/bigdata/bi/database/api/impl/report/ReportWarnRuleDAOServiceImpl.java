package com.bestpay.bigdata.bi.database.api.impl.report;


import com.bestpay.bigdata.bi.common.dto.warn.ReportWarnRuleDTO;
import com.bestpay.bigdata.bi.database.api.report.ReportWarnRuleDAOService;
import com.bestpay.bigdata.bi.database.dao.warn.ReportWarnRuleDo;
import com.bestpay.bigdata.bi.database.mapper.warn.ReportWarnRuleMapper;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * ClassName: ReportWarnRuleDAOServiceImpl
 * Package: com.bestpay.bigdata.bi.database.api.impl
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/8/4 14:45
 * @Version 1.0
 */
@Component
public class ReportWarnRuleDAOServiceImpl implements ReportWarnRuleDAOService {

    @Resource
    private ReportWarnRuleMapper reportWarnRuleMapper;

    @Override
    public int update(ReportWarnRuleDo ruleDo) {
        return reportWarnRuleMapper.update(ruleDo);
    }

    @Override
    public int updateById(ReportWarnRuleDo ruleDo) {
        return reportWarnRuleMapper.updateById(ruleDo);
    }

    @Override
    public int batchInsert(List<ReportWarnRuleDo> list) {
        return reportWarnRuleMapper.batchInsert(list);
    }

    @Override
    public List<ReportWarnRuleDo> query(ReportWarnRuleDTO query) {
        return reportWarnRuleMapper.query(query);
    }

    @Override
    public List<ReportWarnRuleDo> queryBak(ReportWarnRuleDTO reportWarnRuleDTO) {
        return reportWarnRuleMapper.queryBak(reportWarnRuleDTO);
    }
}
