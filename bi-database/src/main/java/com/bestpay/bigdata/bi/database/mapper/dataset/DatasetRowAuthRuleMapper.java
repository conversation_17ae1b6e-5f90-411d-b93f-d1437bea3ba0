package com.bestpay.bigdata.bi.database.mapper.dataset;
import com.bestpay.bigdata.bi.database.dao.dataset.DatasetRowAuthRuleDo;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface DatasetRowAuthRuleMapper {

  void insert(DatasetRowAuthRuleDo ruleDo);

  void updateStatus(@Param(value = "statusCode") int statusCode, @Param(value = "id")Long id);

  List<DatasetRowAuthRuleDo> query(Long datasetId);

  DatasetRowAuthRuleDo queryById(Long id);

  List<DatasetRowAuthRuleDo> queryAll();

  void batchUpdateValue(@Param("rules") List<DatasetRowAuthRuleDo> rules);
}
