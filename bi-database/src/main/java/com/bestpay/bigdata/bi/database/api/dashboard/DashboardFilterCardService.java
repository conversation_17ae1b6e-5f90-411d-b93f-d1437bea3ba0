package com.bestpay.bigdata.bi.database.api.dashboard;

import com.bestpay.bigdata.bi.common.dto.dashboard.FilterCardQueryDTO;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardFilterCardDO;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface DashboardFilterCardService {
    Long insert(DashboardFilterCardDO filterCardDO);

    DashboardFilterCardDO findById(Long id);

    List<DashboardFilterCardDO> find(FilterCardQueryDTO cardQueryDTO);

    void update(Long dashboardId, int statusCode, String updateBy);

    void updateAll(DashboardFilterCardDO filterCardDO);

    // 新增备份表操作方法
    List<DashboardFilterCardDO> findBak(FilterCardQueryDTO cardQueryDTO);

    void batchUpdateAllBak(List<DashboardFilterCardDO> filterCardList);
}
