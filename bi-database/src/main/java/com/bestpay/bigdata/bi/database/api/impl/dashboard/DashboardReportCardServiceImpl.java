package com.bestpay.bigdata.bi.database.api.impl.dashboard;

import com.bestpay.bigdata.bi.common.dto.dashboard.ReportCardQueryDTO;
import com.bestpay.bigdata.bi.common.enums.StatusCodeEnum;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardReportCardService;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardReportCardDO;
import com.bestpay.bigdata.bi.database.mapper.dashboard.DashboardReportCardMapper;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * ClassName: DashboardReportCardServiceImpl
 * Package: com.bestpay.bigdata.bi.database.api.impl.dashboard
 * Description:
 *
 * <AUTHOR>
 * @Create 2024/1/10 13:42
 * @Version 1.0
 */
@Service
@Slf4j
public class DashboardReportCardServiceImpl implements DashboardReportCardService
{
    @Resource
    private DashboardReportCardMapper ReportCardMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insert(DashboardReportCardDO filterCardDO){
        ReportCardMapper.insert(filterCardDO);
        return filterCardDO.getId();
    }

    @Override
    public List<DashboardReportCardDO> find(ReportCardQueryDTO cardQuery){
        return ReportCardMapper.find(cardQuery);
    }


    @Override
    public Long update(DashboardReportCardDO ReportCardDO){
        ReportCardMapper.update(ReportCardDO);
        return ReportCardDO.getId();
    }

    @Override
    public void delete(Long dashboardId,String updateBy){
        DashboardReportCardDO delete = new DashboardReportCardDO();
        delete.setDashboardId(dashboardId);
        delete.setStatusCode(StatusCodeEnum.DELETE.getCode());
        delete.setUpdatedAt(new Date());
        delete.setUpdatedBy(updateBy);
        ReportCardMapper.update(delete);
    }

    @Override
    public void updateTableFontStyleAndTableConfigurationById(DashboardReportCardDO dashboardReportCardDO) {
        ReportCardMapper.updateTableFontStyleAndTableConfigurationById(dashboardReportCardDO);
    }

    @Override
    public List<DashboardReportCardDO> queryByChartTypes(List<Integer> chartTypeLis) {
        return ReportCardMapper.queryByChartTypes(chartTypeLis);
    }

    @Override
    public int updateTableConfigurationById(DashboardReportCardDO toUpdate) {
        Long update = ReportCardMapper.update(toUpdate);
        return Integer.parseInt(String.valueOf( update));
    }

    @Override
    public List<DashboardReportCardDO> findBak(ReportCardQueryDTO cardQuery) {
        return ReportCardMapper.findBak(cardQuery);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateBak(List<DashboardReportCardDO> reportCardList) {
        if (reportCardList != null && !reportCardList.isEmpty()) {
            ReportCardMapper.batchUpdateBak(reportCardList);
        }
    }
}
