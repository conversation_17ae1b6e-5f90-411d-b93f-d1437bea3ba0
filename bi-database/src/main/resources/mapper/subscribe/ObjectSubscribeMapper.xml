<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bestpay.bigdata.bi.database.mapper.subscribe.ObjectSubscribeMapper">

  <resultMap type="com.bestpay.bigdata.bi.database.bean.subscribe.ObjectSubScribeDO" id="BaseMap">
    <result property="id" column="id" jdbcType="BIGINT"/>
    <result property="taskName" column="task_name" jdbcType="VARCHAR"/>
    <result property="subType" column="sub_type" jdbcType="VARCHAR"/>
    <result property="subConfig" column="sub_config" jdbcType="VARCHAR"/>
    <result property="postType" column="post_type" jdbcType="VARCHAR"/>
    <result property="postFrequency" column="post_frequency" jdbcType="VARCHAR"/>
    <result property="startTime" column="start_time" jdbcType="VARCHAR"/>
    <result property="cronExp" column="cron_exp" jdbcType="VARCHAR"/>
    <result property="objectId" column="object_id" jdbcType="BIGINT"/>
    <result property="postUrl" column="post_url" jdbcType="VARCHAR"/>
    <result property="statusCode" column="status_code" jdbcType="INTEGER"/>
    <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
    <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
    <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
    <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
    <result property="objectType" column="object_type" jdbcType="VARCHAR"/>
    <result property="subCode" column="sub_code" jdbcType="VARCHAR"/>
    <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
    <result property="condition" column="query_condition" jdbcType="VARCHAR"/>
    <result property="subscribeContent" column="subscribe_content" jdbcType="VARCHAR"/>

  </resultMap>

  <select id="getById" parameterType="java.lang.Long" resultMap="BaseMap">
      select
      `id`,
      `task_name`,
      `sub_type`,
      `sub_config`,
      `post_type`,
      `post_frequency`,
      `start_time`,
      `cron_exp`,
      `object_id`,
      `post_url`,
      `status_code`,
      `created_at`,
      `created_by`,
      `updated_at`,
      `updated_by`,
      object_type,
      sub_code,
      file_name,
      subscribe_content,
      query_condition
    from t_object_subscribe
    where id = #{id}
  </select>

  <select id="getByCode" parameterType="java.lang.String" resultMap="BaseMap">
    select
      `id`,
      `task_name`,
      `sub_type`,
      `sub_config`,
      `post_type`,
      `post_frequency`,
      `start_time`,
      `cron_exp`,
      `object_id`,
      `post_url`,
      `status_code`,
      `created_at`,
      `created_by`,
      `updated_at`,
      `updated_by`,
      object_type,
      sub_code,
      file_name,
      subscribe_content,
      query_condition
    from t_object_subscribe
    where sub_code = #{subCode} order by id desc limit 1
  </select>

  <select id="queryAll" resultMap="BaseMap">
    select
      `id`,
      `task_name`,
      `sub_type`,
      `sub_config`,
      `post_type`,
      `post_frequency`,
      `start_time`,
      `cron_exp`,
      `object_id`,
      `post_url`,
      `status_code`,
      `created_at`,
      `created_by`,
      `updated_at`,
      `updated_by`,
      object_type,
      sub_code,
      file_name,
      query_condition,
      subscribe_content
    from t_object_subscribe
    where status_code=0
  </select>
    <select id="queryByTaskName"  resultMap="BaseMap">
        SELECT *
        FROM t_object_subscribe
        WHERE status_code != 9
          AND task_name = #{taskName}
        <if test="id != null ">
            AND id != #{id}
        </if>
    </select>
    <select id="queryByObjSubDTO" resultMap="BaseMap">
        SELECT *
        FROM t_object_subscribe
        <where>
            <if test="subScribeDTO.statusCodes != null and subScribeDTO.statusCodes.size > 0">
                AND status_code IN
                <foreach collection="subScribeDTO.statusCodes" open="(" close=")" item="statusCode" separator=",">
                    #{statusCode}
                </foreach>
            </if>
            <if test="subScribeDTO.objectId != null ">
               AND object_id = #{subScribeDTO.objectId}
            </if>

            <if test="subScribeDTO.createdBy != null and subScribeDTO.createdBy != ''">
                AND created_by = #{subScribeDTO.createdBy}
            </if>
            <if test="subScribeDTO.objectType!= null and subScribeDTO.objectType != ''">
                AND object_type = #{subScribeDTO.objectType}
            </if>
            <if test="subScribeDTO.keyword != null and subScribeDTO.keyword != ''">
                AND (sub_code = #{subScribeDTO.keyword} OR task_name = #{subScribeDTO.keyword} )
            </if>
        </where>
        ORDER BY  updated_at DESC
    </select>
    <select id="queryMaxSubCode" resultType="java.lang.String">
        SELECT max(sub_code) FROM t_object_subscribe
    </select>
    <select id="getSubscriptionTaskList" resultType="com.bestpay.bigdata.bi.database.bean.subscribe.SubDTO">

        SELECT
            a.id    AS taskId,
        a.sub_code  AS subCode,
        a.task_name AS taskName,
        a.object_type   AS objectType,
        CASE
        WHEN a.object_type = 'dashboard' THEN b.name
        WHEN a.object_type = 'report' THEN c.report_name
        END AS objectName,
        a.sub_type AS subscribeType,
        a.post_frequency  AS postFrequency,
        a.start_time AS startTime,
        a.created_at AS createdAt,
        a.updated_at  AS updatedAt,
        a.status_code AS statusCode,
        a.post_url  AS postUrl,
        a.created_by  AS createdBy
        FROM t_object_subscribe a
        LEFT JOIN t_dashboard b ON a.object_id = b.id AND a.object_type = 'dashboard'
        LEFT JOIN t_new_report c ON a.object_id = c.id AND a.object_type = 'report'
        WHERE a.object_type IN ('dashboard', 'report')
        <if test="objectSubScribeDTO.statusCodes != null and objectSubScribeDTO.statusCodes.size > 0">
            AND a.status_code IN
            <foreach collection="objectSubScribeDTO.statusCodes" open="(" close=")" item="statusCode" separator=",">
                #{statusCode}
            </foreach>
        </if>
        <if test="objectSubScribeDTO.createdBy != null and objectSubScribeDTO.createdBy != ''">
            AND a.created_by = #{objectSubScribeDTO.createdBy}
        </if>
        <if test="objectSubScribeDTO.keyword != null and objectSubScribeDTO.keyword != ''">
            AND (  a.sub_code LIKE CONCAT('%', #{objectSubScribeDTO.keyword},'%')
            OR a.task_name LIKE CONCAT('%', #{objectSubScribeDTO.keyword},'%')
            OR  c.report_name LIKE CONCAT('%', #{objectSubScribeDTO.keyword},'%')
            OR  b.name LIKE CONCAT('%', #{objectSubScribeDTO.keyword},'%') )
        </if>
        ORDER BY a.updated_at DESC
    </select>

    <!--新增所有列-->
  <insert id="insert" keyProperty="id"
    parameterType="com.bestpay.bigdata.bi.database.bean.subscribe.ObjectSubScribeDO"
    useGeneratedKeys="true">
    insert into t_object_subscribe(`task_name`,
                                   `sub_type`,
                                   `sub_config`,
                                   `post_type`,
                                   `post_frequency`,
                                   `start_time`,
                                   `cron_exp`,
                                   `object_id` ,
                                   `post_url`,
                                   `created_at`,
                                   `created_by`,
                                   `updated_at`,
                                   `updated_by`,
                                   object_type,
                                   sub_code,
                                   file_name,
                                   query_condition,
                                   subscribe_content,
                                   status_code)
    values (#{taskName},
            #{subType},
            #{subConfig},
            #{postType},
            #{postFrequency},
            #{startTime},
            #{cronExp},
            #{objectId},
            #{postUrl},
            #{createdAt},
            #{createdBy},
            #{updatedAt},
            #{updatedBy},
            #{objectType},
            #{subCode},
            #{fileName},
            #{condition},
            #{subscribeContent},
            #{statusCode}
            )
  </insert>


  <update id="updateById" parameterType="com.bestpay.bigdata.bi.database.bean.subscribe.ObjectSubScribeDO">
    update t_object_subscribe
    <set>
        <if test="fileName != null and fileName != ''">
            file_name =  #{fileName},
        </if>
        <if test="condition != null and condition != ''">
          query_condition =  #{condition},
        </if>
        <if test="taskName!=null and taskName!=''">
        task_name = #{taskName},
        </if>
        <if test="subType!=null and subType!=''">
            sub_type = #{subType},
        </if>
        <if test="subConfig!=null and subConfig!=''">
            sub_config = #{subConfig},
        </if>
        <if test="postType!=null and postType!=''">
        `post_type` = #{postType},
        </if>
        <if test="postFrequency!=null and postFrequency!=''">
        `post_frequency` = #{postFrequency},
        </if>
      <if test="subscribeContent!=null and subscribeContent!=''">
        `subscribe_content` = #{subscribeContent},
      </if>
        <if test="startTime!=null and startTime!=''">
        `start_time` = #{startTime},
        </if>
        <if test="cronExp!=null and cronExp!=''">
        `cron_exp` = #{cronExp},
        </if>
        <if test="objectId!=null">
        object_id = #{objectId},
        </if>
        <if test="postUrl!=null and postUrl!=''">
        post_url = #{postUrl},
        </if>
        <if test="statusCode != null">
        status_code = #{statusCode},
        </if>
        <if test="updatedAt != null">
        updated_at = #{updatedAt},
        </if>
        <if test="updatedBy != null">
        updated_by = #{updatedBy}
        </if>
    </set>
    where id =#{id}
  </update>

    <update id="updateByDashboardId" parameterType="com.bestpay.bigdata.bi.database.bean.subscribe.ObjectSubScribeDO">
        update t_object_subscribe
        <set>
            <if test="statusCode != null">
                status_code = #{statusCode},
            </if>
            <if test="updatedAt != null">
                updated_at = #{updatedAt},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy}
            </if>
        </set>
        where object_id =#{objectId}
        and  object_type = 'dashboard'
    </update>

  <select id="queryBySubType"  resultMap="BaseMap">
    SELECT *
    FROM t_object_subscribe
    WHERE status_code != 9
    <if test="subType != null">
      AND sub_type = #{subType}
    </if>
  </select>

  <!-- 查询备份表 -->
  <select id="queryByObjSubDTOBak" resultMap="BaseMap">
        SELECT *
        FROM t_object_subscribe_bak
        <where>
            status_code != 9
            <if test="subScribeDTO.statusCodes != null and subScribeDTO.statusCodes.size > 0">
                AND status_code IN
                <foreach collection="subScribeDTO.statusCodes" item="statusCode" separator="," open="(" close=")">
                    #{statusCode}
                </foreach>
            </if>

            <if test="subScribeDTO.createdBy != null and subScribeDTO.createdBy != ''">
                AND created_by = #{subScribeDTO.createdBy}
            </if>
            <if test="subScribeDTO.objectType!= null and subScribeDTO.objectType != ''">
                AND object_type = #{subScribeDTO.objectType}
            </if>
            <if test="subScribeDTO.keyword != null and subScribeDTO.keyword != ''">
                AND (sub_code = #{subScribeDTO.keyword} OR task_name = #{subScribeDTO.keyword} )
            </if>
        </where>
        ORDER BY  updated_at DESC
    </select>

    <!-- 批量更新备份表，不更新时间字段 -->
    <update id="batchUpdateByIdBak">
        <foreach collection="subscribeList" item="item" separator=";">
            update t_object_subscribe_bak
            <set>
                <if test="item.fileName != null and item.fileName != ''">
                    file_name =  #{item.fileName},
                </if>
                <if test="item.condition != null and item.condition != ''">
                  query_condition =  #{item.condition},
                </if>
                <if test="item.taskName!=null and item.taskName!=''">
                task_name = #{item.taskName},
                </if>
                <if test="item.subType!=null and item.subType!=''">
                    sub_type = #{item.subType},
                </if>
                <if test="item.subConfig!=null and item.subConfig!=''">
                    sub_config = #{item.subConfig},
                </if>
                <if test="item.postType!=null and item.postType!=''">
                `post_type` = #{item.postType},
                </if>
                <if test="item.postFrequency!=null and item.postFrequency!=''">
                `post_frequency` = #{item.postFrequency},
                </if>
              <if test="item.subscribeContent!=null and item.subscribeContent!=''">
                subscribe_content = #{item.subscribeContent},
              </if>
              <if test="item.startTime!=null">
                `start_time` = #{item.startTime},
              </if>
              <if test="item.cronExp!=null and item.cronExp!=''">
                `cron_exp` = #{item.cronExp},
              </if>
              <if test="item.objectId!=null">
                `object_id` = #{item.objectId},
              </if>
              <if test="item.postUrl!=null and item.postUrl!=''">
                `post_url` = #{item.postUrl},
              </if>
              <if test="item.statusCode!=null">
                `status_code` = #{item.statusCode},
              </if>
              <if test="item.objectType!=null and item.objectType!=''">
                object_type = #{item.objectType},
              </if>
              <if test="item.subCode!=null and item.subCode!=''">
                sub_code = #{item.subCode}
              </if>
            </set>
            where id = #{item.id}
        </foreach>
    </update>

</mapper>

