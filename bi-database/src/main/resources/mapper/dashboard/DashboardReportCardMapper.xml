<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bestpay.bigdata.bi.database.mapper.dashboard.DashboardReportCardMapper">

    <resultMap type="com.bestpay.bigdata.bi.database.dao.dashboard.DashboardReportCardDO" id="DashboardReportCardDOMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="dashboardId" column="dashboard_id" jdbcType="INTEGER"/>
        <result property="orgSelected" column="org_selected" jdbcType="VARCHAR"/>
        <result property="reportName" column="report_name" jdbcType="VARCHAR"/>
        <result property="reportDesc" column="report_desc" jdbcType="VARCHAR"/>
        <result property="dataAuth" column="data_auth" jdbcType="VARCHAR"/>
        <result property="showColumn" column="show_column" jdbcType="VARCHAR"/>
        <result property="indexColumn" column="index_column" jdbcType="VARCHAR"/>
        <result property="filterColumn" column="filter_column" jdbcType="VARCHAR"/>
        <result property="condition" column="con_control" jdbcType="VARCHAR"/>
        <result property="keyword" column="keyword" jdbcType="VARCHAR"/>
        <result property="orderColumn" column="order_column" jdbcType="VARCHAR"/>
        <result property="computeColumn" column="compute_column" jdbcType="VARCHAR"/>
        <result property="contrastColumn" column="contrast_column" jdbcType="VARCHAR"/>
        <result property="datasetInfo" column="dataset_info" jdbcType="VARCHAR"/>
        <result property="reportType" column="report_type" jdbcType="INTEGER"/>
        <result property="reportStructure" column="report_structure" jdbcType="VARCHAR"/>
        <result property="fileContainSensitiveInfo" column="file_contain_sensitive_info" jdbcType="INTEGER"/>
        <result property="sensitiveFields" column="sensitive_fields" jdbcType="VARCHAR"/>
        <result property="statusCode" column="status_code" jdbcType="INTEGER"/>
        <result property="rollupDown" column="rollup_down" jdbcType="INTEGER"/>
        <result property="showIndexTotal" column="show_index_total" jdbcType="VARCHAR"/>
        <result property="chartType" column="chart_type" jdbcType="INTEGER"/>
        <result property="chartField" column="chart_field" jdbcType="VARCHAR"/>
        <result property="maxRows" column="max_rows" jdbcType="VARCHAR"/>
        <result property="tableConfiguration" column="table_configuration" jdbcType="VARCHAR"/>
        <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="tableFontStyle" column="table_font_style" />
        <result property="coordinateAxisConfig" column="coordinate_axis_config" />
        <result property="cardStyleConfig" column="card_style_config" />
        <result property="graphicDisplay" column="graphic_display" />
        <result property="orderType" column="order_type" jdbcType="INTEGER"/>
    </resultMap>


    <!-- 插入指标文本卡片数据 -->
    <insert id="insert" parameterType="com.bestpay.bigdata.bi.database.dao.dashboard.DashboardReportCardDO">
        <selectKey resultType="Long" keyProperty="id" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        INSERT INTO t_dashboard_report_card (dashboard_id,
                                             org_selected,
                                             report_name,
                                             report_desc,
                                             data_auth,
                                             show_column,
                                             index_column,
                                             filter_column,
                                             con_control,
                                             keyword,order_column,
                                             compute_column,
                                             contrast_column,
                                             dataset_info,
                                             report_type,
                                             report_structure,
                                             file_contain_sensitive_info,
                                             sensitive_fields,
                                             chart_type,
                                             chart_field,
                                             show_index_total,
                                             status_code,
                                             max_rows,
                                             table_configuration,
                                             rollup_down,
                                             created_at,
                                             created_by,
                                             updated_at,
                                             updated_by,
                                             table_font_style,
                                             coordinate_axis_config,
                                             card_style_config,
                                            graphic_display,
                                            dataset_id,
                                             order_type
                                             )
        VALUES(#{dashboardId},
            #{orgSelected},
            #{reportName},
            #{reportDesc},
            #{dataAuth},
            #{showColumn},
            #{indexColumn},
            #{filterColumn},
            #{condition},
            #{keyword},
            #{orderColumn},
            #{computeColumn},
            #{contrastColumn},
            #{datasetInfo},
            #{reportType},
            #{reportStructure},
            #{fileContainSensitiveInfo},
            #{sensitiveFields},
            #{chartType},
            #{chartField},
            #{showIndexTotal},
            #{statusCode},
            #{maxRows},
            #{tableConfiguration},
            #{rollupDown},
            #{createdAt},
            #{createdBy},
            #{updatedAt},
            #{updatedBy},
            #{tableFontStyle},
            #{coordinateAxisConfig},
            #{cardStyleConfig},
            #{graphicDisplay},
            #{datasetId},
            #{orderType})
    </insert>


    <select id="find" resultMap="DashboardReportCardDOMap">
        SELECT
            id,
            dashboard_id,
            org_selected,
            report_name,
            report_desc,
            data_auth,
            show_column,
            index_column,
            filter_column,
            con_control,
            keyword,
            order_column,
            compute_column,
            contrast_column,
            dataset_info,
            report_type,
            report_structure,
            file_contain_sensitive_info,
            sensitive_fields,
            chart_type,
            chart_field,
            max_rows,
            show_index_total,
            status_code,
            rollup_down,
            table_configuration,
            created_at,
            created_by,
            updated_at,
            updated_by,
            table_font_style,
            coordinate_axis_config,
            card_style_config,
            graphic_display,
            order_type
            FROM t_dashboard_report_card
        <where>
            status_code !=9
            <if test="dashboardId != null">
                and dashboard_id = #{dashboardId}
            </if>
            <if test="datasetId != null"> AND dataset_id = #{datasetId}</if>
            <if test="null != idList and idList.size > 0">
                and id in
                <foreach collection="idList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="queryAll" resultMap="DashboardReportCardDOMap">
        SELECT * FROM t_dashboard_report_card WHERE status_code != 9;
    </select>
    <select id="queryByChartTypes" resultMap="DashboardReportCardDOMap">
        SELECT
        id,
        dashboard_id,
        org_selected,
        report_name,
        report_desc,
        data_auth,
        show_column,
        index_column,
        filter_column,
        con_control,
        keyword,
        order_column,
        compute_column,
        contrast_column,
        dataset_info,
        report_type,
        report_structure,
        file_contain_sensitive_info,
        sensitive_fields,
        chart_type,
        chart_field,
        max_rows,
        show_index_total,
        status_code,
        rollup_down,
        table_configuration,
        created_at,
        created_by,
        updated_at,
        updated_by,
        table_font_style,
        coordinate_axis_config,
        card_style_config,
        graphic_display,
        order_type
        FROM t_dashboard_report_card
        <where>
            status_code !=9
            <if test="null != chartTypeList and chartTypeList.size > 0">
                and chart_type in
                <foreach collection="chartTypeList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>


    <!--通过主键修改数据-->
    <update id="update"
    parameterType="com.bestpay.bigdata.bi.database.dao.dashboard.DashboardReportCardDO">
        update t_dashboard_report_card
        <set>
            <if test="coordinateAxisConfig != null and coordinateAxisConfig != ''">
                coordinate_axis_config = #{coordinateAxisConfig},
            </if>
            <if test="reportName != null and reportName != ''">
                report_name = #{reportName},
            </if>
            <if test="orgSelected != null and orgSelected != ''">
                org_selected = #{orgSelected},
            </if>
            <if test="reportDesc != null and reportDesc != ''">
                report_desc = #{reportDesc},
            </if>
            <if test="dataAuth != null and dataAuth != ''">
                data_auth = #{dataAuth},
            </if>
            <if test="showColumn != null and showColumn != ''">
                show_column = #{showColumn},
            </if>
            <if test="indexColumn != null and indexColumn != ''">
                index_column = #{indexColumn},
            </if>
            <if test="filterColumn != null and filterColumn != ''">
                filter_column = #{filterColumn},
            </if>
            <if test="condition != null and condition != ''">
                con_control = #{condition},
            </if>
            <if test="keyword != null and keyword != ''">
                keyword = #{keyword},
            </if>
            <if test="orderColumn != null and orderColumn != ''">
                order_column = #{orderColumn},
            </if>
            <if test="computeColumn != null and computeColumn != ''">
                compute_column = #{computeColumn},
            </if>
            <if test="contrastColumn != null and contrastColumn != ''">
                contrast_column = #{contrastColumn},
            </if>
            <if test="datasetInfo != null and datasetInfo != ''">
                dataset_info = #{datasetInfo},
            </if>
            <if test="reportType != null and reportType != ''">
                report_type = #{reportType},
            </if>
            <if test="reportStructure != null and reportStructure != ''">
                report_structure = #{reportStructure},
            </if>
            <if test="fileContainSensitiveInfo != null">
                file_contain_sensitive_info = #{fileContainSensitiveInfo},
            </if>
            <if test="sensitiveFields != null and sensitiveFields != ''">
                sensitive_fields = #{sensitiveFields},
            </if>
            <if test="chartType != null">
                chart_type = #{chartType},
            </if>
            <if test="chartField != null and chartField != ''">
                chart_field = #{chartField},
            </if>
            <if test="showIndexTotal != null and showIndexTotal != ''">
                show_index_total = #{showIndexTotal},
            </if>
            <if test="tableConfiguration != null and tableConfiguration != ''">
                table_configuration = #{tableConfiguration},
            </if>
            <if test="statusCode != null">
                status_code = #{statusCode},
            </if>
            <if test="rollupDown != null">
                rollup_down = #{rollupDown},
            </if>
            <if test="createdAt != null">
                created_at = #{createdAt},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="updatedAt != null">
                updated_at = #{updatedAt},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                updated_by = #{updatedBy},
            </if>
            <if test="maxRows != null">
                max_rows = #{maxRows},
            </if>
            <if test="cardStyleConfig != null">
                card_style_config = #{cardStyleConfig}
            </if>
        </set>
        <where>
            status_code !=9
            <if test="dashboardId != null">
                AND dashboard_id = #{dashboardId}
            </if>
            <if test="id != null">
                AND id = #{id}
            </if>
        </where>
    </update>
    <update id="updateTableFontStyleAndTableConfigurationById">
        UPDATE t_dashboard_report_card
        SET table_font_style = #{dashboardReportCardDO.tableFontStyle},
            table_configuration = #{dashboardReportCardDO.tableConfiguration}
        where id = #{dashboardReportCardDO.id}
    </update>

    <!-- 查询备份表 -->
    <select id="findBak" resultMap="DashboardReportCardDOMap">
        SELECT
            id,
            dashboard_id,
            org_selected,
            report_name,
            report_desc,
            data_auth,
            show_column,
            index_column,
            filter_column,
            con_control,
            keyword,
            order_column,
            compute_column,
            contrast_column,
            dataset_info,
            report_type,
            report_structure,
            file_contain_sensitive_info,
            sensitive_fields,
            chart_type,
            chart_field,
            max_rows,
            show_index_total,
            status_code,
            rollup_down,
            table_configuration,
            created_at,
            created_by,
            updated_at,
            updated_by,
            table_font_style,
            coordinate_axis_config,
            card_style_config,
            graphic_display,
            order_type
            FROM t_dashboard_report_card_bak
        <where>
            status_code !=9
            <if test="dashboardId != null">
                and dashboard_id = #{dashboardId}
            </if>
            <if test="datasetId != null"> AND dataset_id = #{datasetId}</if>
            <if test="null != idList and idList.size > 0">
                and id in
                <foreach collection="idList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <!-- 批量更新备份表，不更新时间字段 -->
    <update id="batchUpdateBak">
        <foreach collection="reportCardList" item="item" separator=";">
            update t_dashboard_report_card_bak
            <set>
                <if test="item.coordinateAxisConfig != null and item.coordinateAxisConfig != ''">
                    coordinate_axis_config = #{item.coordinateAxisConfig},
                </if>
                <if test="item.reportName != null and item.reportName != ''">
                    report_name = #{item.reportName},
                </if>
                <if test="item.orgSelected != null and item.orgSelected != ''">
                    org_selected = #{item.orgSelected},
                </if>
                <if test="item.reportDesc != null and item.reportDesc != ''">
                    report_desc = #{item.reportDesc},
                </if>
                <if test="item.dataAuth != null and item.dataAuth != ''">
                    data_auth = #{item.dataAuth},
                </if>
                <if test="item.showColumn != null and item.showColumn != ''">
                    show_column = #{item.showColumn},
                </if>
                <if test="item.indexColumn != null and item.indexColumn != ''">
                    index_column = #{item.indexColumn},
                </if>
                <if test="item.filterColumn != null and item.filterColumn != ''">
                    filter_column = #{item.filterColumn},
                </if>
                <if test="item.condition != null and item.condition != ''">
                    con_control = #{item.condition},
                </if>
                <if test="item.keyword != null and item.keyword != ''">
                    keyword = #{item.keyword},
                </if>
                <if test="item.orderColumn != null and item.orderColumn != ''">
                    order_column = #{item.orderColumn},
                </if>
                <if test="item.computeColumn != null and item.computeColumn != ''">
                    compute_column = #{item.computeColumn},
                </if>
                <if test="item.contrastColumn != null and item.contrastColumn != ''">
                    contrast_column = #{item.contrastColumn},
                </if>
                <if test="item.datasetInfo != null and item.datasetInfo != ''">
                    dataset_info = #{item.datasetInfo},
                </if>
                <if test="item.reportType != null">
                    report_type = #{item.reportType},
                </if>
                <if test="item.reportStructure != null and item.reportStructure != ''">
                    report_structure = #{item.reportStructure},
                </if>
                <if test="item.fileContainSensitiveInfo != null">
                    file_contain_sensitive_info = #{item.fileContainSensitiveInfo},
                </if>
                <if test="item.sensitiveFields != null and item.sensitiveFields != ''">
                    sensitive_fields = #{item.sensitiveFields},
                </if>
                <if test="item.chartType != null">
                    chart_type = #{item.chartType},
                </if>
                <if test="item.chartField != null and item.chartField != ''">
                    chart_field = #{item.chartField},
                </if>
                <if test="item.showIndexTotal != null">
                    show_index_total = #{item.showIndexTotal},
                </if>
                <if test="item.statusCode != null">
                    status_code = #{item.statusCode},
                </if>
                <if test="item.maxRows != null">
                    max_rows = #{item.maxRows},
                </if>
                <if test="item.tableConfiguration != null and item.tableConfiguration != ''">
                    table_configuration = #{item.tableConfiguration},
                </if>
                <if test="item.rollupDown != null">
                    rollup_down = #{item.rollupDown},
                </if>
                <if test="item.tableFontStyle != null and item.tableFontStyle != ''">
                    table_font_style = #{item.tableFontStyle},
                </if>
                <if test="item.cardStyleConfig != null and item.cardStyleConfig != ''">
                    card_style_config = #{item.cardStyleConfig},
                </if>
                <if test="item.graphicDisplay != null and item.graphicDisplay != ''">
                    graphic_display = #{item.graphicDisplay},
                </if>
                <if test="item.datasetId != null">
                    dataset_id = #{item.datasetId},
                </if>
                <if test="item.orderType != null">
                    order_type = #{item.orderType}
                </if>
            </set>
            <where>
                status_code !=9
                <if test="item.dashboardId != null">
                    AND dashboard_id = #{item.dashboardId}
                </if>
                <if test="item.id != null">
                    AND id = #{item.id}
                </if>
            </where>
        </foreach>
    </update>


</mapper>