<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bestpay.bigdata.bi.database.mapper.dashboard.DashboardIndexTextCardMapper">

    <resultMap id="dashboardIndexTextCardResultMap"
               type="com.bestpay.bigdata.bi.database.dao.dashboard.DashboardIndexTextCardDO">
        <id column="id" property="id"/>
        <result column="dashboard_id" property="dashboardId"/>
        <result column="index_info" property="indexInfo"/>
        <result column="drag_result" property="dragResult"/>
        <result column="filterdrag_result" property="filterdragResult"/>
        <result column="count_filed_list" property="countFiledList"/>
        <result column="index_card_type" property="indexCardType"/>
        <result column="card_style_config" property="cardStyleConfig"/>
        <result column="status_code" property="statusCode"/>
        <result column="created_at" property="createdAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="updated_by" property="updatedBy"/>
    </resultMap>

    <select id="find" resultMap="dashboardIndexTextCardResultMap">
        SELECT * FROM t_dashboard_index_text_card
        <where>
            status_code !=9
            <if test="datasetId != null"> AND dataset_id = #{datasetId}</if>
            <if test="dashboardId != null">
                and dashboard_id = #{dashboardId}
            </if>
            <if test="indexCardType != null">
                and index_card_type = #{indexCardType}
            </if>
            <if test="null != idList and idList.size > 0">
                and id in
                <foreach collection="idList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="queryByDashboardIdList"  resultMap="dashboardIndexTextCardResultMap">
        SELECT * FROM t_dashboard_index_text_card
        <where>
            status_code !=9
            <if test="indexCardType != null and indexCardType != ''">
                and index_card_type = #{indexCardType}
            </if>
            <if test="dashboardIdList != null  and dashboardIdList.size > 0">
                and dashboard_id in
                <foreach collection="dashboardIdList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <!-- 插入指标文本卡片数据 -->
    <insert id="insert" parameterType="com.bestpay.bigdata.bi.database.dao.dashboard.DashboardIndexTextCardDO">
        <selectKey resultType="Long" keyProperty="id" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        INSERT INTO t_dashboard_index_text_card (
        dashboard_id,
        index_info,
        drag_result,
        filterdrag_result,
        count_filed_list,
        index_card_type,
        card_style_config,
        created_by,
        updated_by,
        dataset_id)
        VALUES
        (#{dashboardId},
        #{indexInfo},
        #{dragResult},
        #{filterdragResult},
        #{countFiledList},
        #{indexCardType},
        #{cardStyleConfig},
        #{createdBy},
        #{updatedBy},
        #{datasetId})
    </insert>
    <insert id="batchInsert">
        INSERT INTO t_dashboard_index_text_card (
            dashboard_id,
            index_info,
            drag_result,
            filterdrag_result,
            count_filed_list,
            index_card_type,
            created_by,
            updated_by)
        VALUES
        <foreach item="item" collection="dashboardIndexTextCardDOList" separator=",">
            (#{item.dashboardId},
             #{item.indexInfo},
             #{item.dragResult},
             #{item.filterdragResult},
             #{item.countFiledList},
             #{item.indexCardType},
             #{item.createdBy},
             #{item.updatedBy})
        </foreach>
    </insert>

    <update id="update"
            parameterType="com.bestpay.bigdata.bi.database.dao.dashboard.DashboardIndexTextCardDO">
        UPDATE t_dashboard_index_text_card
        <set>
            <if test="statusCode != null">
                status_code = #{statusCode},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy},
            </if>
            <if test="indexInfo != null">
                index_info = #{indexInfo},
            </if>
            <if test="dragResult != null">
                drag_result = #{dragResult},
            </if>
            <if test="filterdragResult != null">
                filterdrag_result = #{filterdragResult},
            </if>
            <if test="countFiledList != null">
                count_filed_list = #{countFiledList},
            </if>
            updated_at = now()
        </set>
        <where>
            status_code !=9
            <if test="dashboardId != null">
                AND dashboard_id = #{dashboardId}
            </if>
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="indexCardType != null">
                AND index_card_type = #{indexCardType}
            </if>
        </where>
    </update>
</mapper>