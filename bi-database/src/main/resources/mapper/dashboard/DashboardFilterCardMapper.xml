<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bestpay.bigdata.bi.database.mapper.dashboard.DashboardFilterCardMapper">

    <resultMap id="DashboardFilterCardResultMap"
               type="com.bestpay.bigdata.bi.database.dao.dashboard.DashboardFilterCardDO">
        <id column="id" property="id"/>
        <result column="dataset_id" property="datasetId"/>
        <result column="dashboard_id" property="dashboardId"/>
        <result column="fields" property="fields"/>
        <result column="filter_date_type" property="filterDateType"/>
        <result column="is_association" property="isAssociation"/>
        <result column="relate_cards_info" property="relateCardsInfo"/>
        <result column="data_set_info" property="dataSetInfo"/>
        <result column="status_code" property="statusCode"/>
        <result column="created_at" property="createdAt"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="updated_by" property="updatedBy"/>
    </resultMap>

    <insert id="insert" parameterType="com.bestpay.bigdata.bi.database.dao.dashboard.DashboardFilterCardDO">
        <selectKey resultType="Long" keyProperty="id" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        INSERT INTO t_dashboard_filter_card (
            dataset_id,
            dashboard_id,
            fields,
            filter_date_type,
            is_association,
            relate_cards_info,
            created_by,
            updated_by
        ) VALUES (
                     #{datasetId},
                     #{dashboardId},
                     #{fields},
                     #{filterDateType},
                     #{isAssociation},
                     #{relateCardsInfo},
                     #{createdBy},
                     #{updatedBy}
                 )
    </insert>

    <select id="findById" resultMap="DashboardFilterCardResultMap" parameterType="java.lang.Long">
        SELECT * FROM t_dashboard_filter_card WHERE id = #{id}
    </select>

    <select id="find" resultMap="DashboardFilterCardResultMap">
        SELECT * FROM t_dashboard_filter_card
        <where>
            status_code !=9
            <if test="dashboardId != null">
                and dashboard_id = #{dashboardId}
            </if>
            <if test="datasetId != null"> AND dataset_id = #{datasetId}</if>
            <if test="null != idList and idList.size > 0">
                and id in
                <foreach collection="idList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <update id="update" parameterType="com.bestpay.bigdata.bi.database.dao.dashboard.DashboardFilterCardDO">
        UPDATE t_dashboard_filter_card
        <set>
            <if test="statusCode != null">
                status_code = #{statusCode},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy},
            </if>
            updated_at = now()
        </set>
        <where>
            status_code !=9 AND
            <if test="dashboardId != null">
                dashboard_id = #{dashboardId}
            </if>
        </where>
    </update>


    <update id="updateAll">
        UPDATE t_dashboard_filter_card
        <set>
            <if test="fields != null and fields != ''">
                fields = #{fields},
            </if>
            <if test="relateCardsInfo != null and relateCardsInfo != ''">
                relate_cards_info = #{relateCardsInfo},
            </if>
            <if test="datasetId != null and datasetId != ''">
                dataset_id = #{datasetId},
            </if>
        </set>
        <where>
            status_code !=9 AND
            <if test="id != null">
                id = #{id}
            </if>
        </where>

    </update>

    <!-- 查询备份表 -->
    <select id="findBak" resultMap="DashboardFilterCardResultMap">
        SELECT * FROM t_dashboard_filter_card_bak
        <where>
            status_code !=9
            <if test="datasetId != null"> AND dataset_id = #{datasetId}</if>
            <if test="dashboardId != null">
                and dashboard_id = #{dashboardId}
            </if>
            <if test="null != idList and idList.size > 0">
                and id in
                <foreach collection="idList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <!-- 批量更新备份表，不更新时间字段 -->
    <update id="batchUpdateAllBak">
        <foreach collection="filterCardList" item="item" separator=";">
            UPDATE t_dashboard_filter_card_bak
            <set>
                <if test="item.fields != null and item.fields != ''">
                    fields = #{item.fields},
                </if>
                <if test="item.relateCardsInfo != null and item.relateCardsInfo != ''">
                    relate_cards_info = #{item.relateCardsInfo},
                </if>
                <if test="item.datasetId != null">
                    dataset_id = #{item.datasetId},
                </if>
                <if test="item.filterDateType != null">
                    filter_date_type = #{item.filterDateType},
                </if>
                <if test="item.isAssociation != null">
                    is_association = #{item.isAssociation},
                </if>
                <if test="item.dataSetInfo != null and item.dataSetInfo != ''">
                    data_set_info = #{item.dataSetInfo},
                </if>
                <if test="item.statusCode != null">
                    status_code = #{item.statusCode}
                </if>
            </set>
            <where>
                status_code !=9 AND
                <if test="item.id != null">
                    id = #{item.id}
                </if>
            </where>
        </foreach>
    </update>

</mapper>