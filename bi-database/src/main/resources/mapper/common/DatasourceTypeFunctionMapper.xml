<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bestpay.bigdata.bi.database.mapper.common.DatasourceTypeFunctionMapper">

    <resultMap type="com.bestpay.bigdata.bi.database.bean.DatasourceTypeFunctionDo" id="FunctionMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="datasourceType" column="datasource_type" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="isAggregate" column="is_aggregate" jdbcType="INTEGER"/>
        <result property="caseInsensitive" column="case_insensitive" jdbcType="INTEGER"/>
        <result property="aliseTo" column="alise_to" jdbcType="VARCHAR"/>
        <result property="statusCode" column="status_code" jdbcType="INTEGER"/>
        <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- insert sql test record into table -->
    <insert id="insertDatasourceTypeFunctionDo" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO t_datasource_type_function
        (datasource_type,
         name,
         `is_aggregate`,
         `case_insensitive`,
         `alise_to`,
         created_at,
         created_by,
         updated_at,
         updated_by)
        VALUES (
                   #{datasourceType},
                   #{name},
                   #{isAggregate},
                   #{caseInsensitive} ,
                   #{aliseTo},
                   #{createdAt},
                   #{createdBy},
                   #{updatedAt},
                   #{updatedBy})
    </insert>


    <select id="queryList" resultMap="FunctionMap" >
        SELECT * FROM t_datasource_type_function
        <where>
            status_code != 9
            <if test="datasourceType != null and datasourceType != ''">
                and datasource_type = #{datasourceType}
            </if>
        </where>
    </select>
</mapper>