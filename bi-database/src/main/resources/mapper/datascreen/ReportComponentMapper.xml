<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bestpay.bigdata.bi.database.mapper.datascreen.ReportComponentMapper">

  <resultMap id="BaseResultMap" type="com.bestpay.bigdata.bi.database.dao.datascreen.ReportComponentDO">
    <id column="id" property="id" jdbcType="BIGINT"/>
    <result column="report_name" property="reportName" jdbcType="VARCHAR"/>
    <result column="report_desc" property="reportDesc" jdbcType="VARCHAR"/>
    <result column="data_auth" property="dataAuth" jdbcType="VARCHAR"/>
    <result column="dataset_id" property="datasetId" jdbcType="BIGINT"/>
    <result column="status_code" property="statusCode" jdbcType="INTEGER"/>
    <result column="report_structure" property="reportStructure" jdbcType="LONGVARCHAR"/>
    <result column="created_at" property="createdAt" jdbcType="TIMESTAMP"/>
    <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
    <result column="updated_at" property="updatedAt" jdbcType="TIMESTAMP"/>
    <result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
    <result column="org_selected" property="orgSelected" jdbcType="VARCHAR"/>
    <result column="coordinate_axis_config" property="coordinateAxisConfig" jdbcType="VARCHAR"/>
    <result column="order_type" property="orderType" jdbcType="INTEGER"/>
  </resultMap>


    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        <selectKey resultType="Long" keyProperty="id" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        INSERT INTO t_report_component (report_name,
                                        report_desc,
                                        data_auth,
                                        dataset_id,
                                        report_structure,
                                        order_type,
                                        created_by,
                                        updated_by,
                                        org_selected,coordinate_axis_config)
        VALUES (#{reportName},
            #{reportDesc},
            #{dataAuth},
            #{datasetId},
            #{reportStructure},
            #{orderType},
            #{createdBy},
            #{updatedBy},
            #{orgSelected,jdbcType=VARCHAR},#{coordinateAxisConfig})
    </insert>



    <insert id="insertShade">
        INSERT INTO t_report_shade_component (
        id,
        report_name,
        report_desc,
        data_auth,
        dataset_id,
        report_structure,
        order_type,
        created_at,
        created_by,
        updated_at,
        updated_by,
        org_selected,coordinate_axis_config)
        VALUES (
        #{id},
        #{reportName},
        #{reportDesc},
        #{dataAuth},
        #{datasetId},
        #{reportStructure},
        #{orderType},
        #{createdAt},
        #{createdBy},
        #{updatedAt},
        #{updatedBy},
        #{orgSelected,jdbcType=VARCHAR},#{coordinateAxisConfig})
    </insert>


    <select id="findAll" resultMap="BaseResultMap" >
        SELECT * FROM t_report_component
        <where>
            status_code != 9
        </where>
    </select>


    <select id="findShadeAll" resultMap="BaseResultMap" >
        SELECT * FROM t_report_shade_component
        <where>
            status_code != 9
        </where>
    </select>


    <update id="update" parameterType="com.bestpay.bigdata.bi.database.dao.datascreen.ReportComponentDO">
        UPDATE t_report_component
        <set>
            <if test="reportName != null">
                report_name = #{reportName},
            </if>
            <if test="reportDesc != null">
                report_desc = #{reportDesc},
            </if>
            <if test="dataAuth != null">
                data_auth = #{dataAuth},
            </if>
            <if test="datasetId != null">
                dataset_id = #{datasetId},
            </if>
            <if test="statusCode != null">
                status_code = #{statusCode},
            </if>
            <if test="reportStructure != null">
                report_structure = #{reportStructure},
            </if>
            <if test="createdAt != null">
                created_at = #{createdAt},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy},
            </if>
            <if test="updatedAt != null">
                updated_at = #{updatedAt},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy}
            </if>
            <if test="orgSelected != null">
                org_selected = #{orgSelected}
            </if>
            <if test="coordinateAxisConfig != null">
                coordinate_axis_config = #{coordinateAxisConfig}
            </if>
        </set>
        <where>
            status_code != 9
            <if test="id != null">
                AND id = #{id}
            </if>
        </where>
    </update>


    <select id="findById" resultMap="BaseResultMap">
        select * from t_report_component where status_code!=9 and id=#{id,jdbcType=BIGINT}
    </select>
    <select id="queryByIdListAndDataSetId"  resultMap="BaseResultMap">
        SELECT * FROM t_report_component
        <where>
            status_code!=9
            <if test="dataScreenReportCardIdList != null and dataScreenReportCardIdList.size > 0">
                AND id IN
                <foreach collection="dataScreenReportCardIdList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="datasetId != null">
                AND dataset_id = #{datasetId}
            </if>
        </where>
    </select>
    <select id="queryByChartTypeList"
            resultType="com.bestpay.bigdata.bi.database.dao.datascreen.AxisAndChartFieldDO">
        SELECT c.id AS reportId , c.coordinate_axis_config as coordinateAxisConfig ,d.id  as reportConfId,d.chart_field as chartField
        FROM t_datascreen  a
                 INNER JOIN t_component b  ON a.id = b.resource_id
                 INNER JOIN t_report_component c ON b.card_id = c.id
                 INNER JOIN t_report_style_config d ON c.id = d.report_id
        WHERE a.status_code!= 9 and b.status_code!= 9 AND c.status_code!= 9 AND d.status_code!= 9 AND b.card_type = 'report'
        <if test="null != chartTypeList and chartTypeList.size > 0">
            AND d.chart_type in
            <foreach collection="chartTypeList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>
