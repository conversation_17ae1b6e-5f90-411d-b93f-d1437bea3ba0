<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bestpay.bigdata.bi.database.mapper.report.DataProvinceAdministratorMapper">

  <resultMap type="com.bestpay.bigdata.bi.database.bean.DataProvinceAdministrator" id="BaseMap">
    <result property="id" column="id" jdbcType="BIGINT"/>
    <result property="orgCode" column="org_code" jdbcType="VARCHAR"/>
    <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
      <result property="provinceCode" column="province_code" jdbcType="VARCHAR"/>
      <result property="provinceName" column="province_name" jdbcType="VARCHAR"/>
    <result property="admin" column="admin" jdbcType="VARCHAR"/>
    <result property="adminJiraAccount" column="admin_jira_account" jdbcType="VARCHAR"/>
    <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
    <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
    <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
    <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
  </resultMap>

  <select id="queryAll" resultMap="BaseMap">
    select `id`,
           `org_code`,
           `org_name`,
           `province_code`,
           `province_name`,
           `admin`,
           `admin_jira_account`
    from data_province_administrator
  </select>
</mapper>

