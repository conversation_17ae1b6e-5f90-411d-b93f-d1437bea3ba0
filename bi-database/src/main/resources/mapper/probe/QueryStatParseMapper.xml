<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bestpay.bigdata.bi.database.mapper.probe.QueryStatParseMapper">

    <resultMap type="com.bestpay.bigdata.bi.database.dao.probe.QueryStatParseDo" id="QueryStatParseMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="username" column="user_name" jdbcType="VARCHAR"/>
        <result property="userOrg" column="user_org" jdbcType="VARCHAR"/>
        <result property="requestSystem" column="request_system" jdbcType="VARCHAR"/>
        <result property="dbName" column="db_name" jdbcType="VARCHAR"/>
        <result property="tableName" column="table_name" jdbcType="VARCHAR"/>
        <result property="statementType" column="statement_type" jdbcType="VARCHAR"/>
        <result property="engineName" column="engine_name" jdbcType="VARCHAR"/>
        <result property="datasourceName" column="datasource_name" jdbcType="VARCHAR"/>
        <result property="sql" column="query_sql" jdbcType="VARCHAR"/>
        <result property="queryId" column="query_id" jdbcType="VARCHAR"/>
        <result property="startTime" column="start_time" jdbcType="DATE"/>
    </resultMap>

    <insert id="insert" keyProperty="id" useGeneratedKeys="true" >
        insert t_querystat_parse (`user_name`, `user_org`, `request_system`, `db_name`, `table_name`, `statement_type`, `engine_name`, `datasource_name`,
               `sql`, `query_id`, `start_time`)
        values (#{username}, #{userOrg}, #{requestSystem}, #{dbName}, #{tableName}, #{statementType}, #{engineName}, #{datasourceName}, #{sql}, #{queryId}, #{startTime})
    </insert>
</mapper>