package com.bestpay.bigdata.bi.backend.cache;

import com.bestpay.bigdata.bi.common.common.ResultTypeEnum;
import com.bestpay.bigdata.bi.common.entity.QueryContext;

import java.util.Optional;

/**
 * The interface Cache.
 *
 * @param <T> the type parameter
 * <AUTHOR>
 * @date 2022 /3/1 15:30
 */
public interface Cache<T> {

    /**
     * Is hit cache boolean.
     *
     * @param cacheKey the cache key
     * @return the boolean
     */
    boolean isHitCache(String cacheKey);


    /**
     * Gets cache data.
     *
     * @param cacheKey the cache key
     * @return the cache data
     */
    Optional<T> getCacheData(String cacheKey);

    /**
     * Clear cache data.
     *
     * @param cacheKey the cache key
     */
    void clearCacheData(String cacheKey);

    /**
     * refresh the key expired time.
     * @param cacheKey
     */
    void refreshExpiredTime(String cacheKey);

    /**
     * cache type
     * @return CacheTypeEnum
     */
    ResultTypeEnum getResultType();

    /**
     * cache data
     * @param queryContext
     */
    void cacheResultIfSucceed(QueryContext queryContext);
}
