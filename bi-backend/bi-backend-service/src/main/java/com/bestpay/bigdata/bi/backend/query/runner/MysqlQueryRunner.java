package com.bestpay.bigdata.bi.backend.query.runner;

import cn.hutool.core.math.MathUtil;
import cn.hutool.core.util.NumberUtil;
import com.bestpay.bigdata.bi.common.common.Constant;
import com.bestpay.bigdata.bi.common.enums.QueryType;
import com.bestpay.bigdata.bi.common.execution.DataManage;
import com.bestpay.bigdata.bi.common.config.ApolloRefreshConfig;
import com.bestpay.bigdata.bi.common.entity.QueryContext;
import com.bestpay.bigdata.bi.common.util.StringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.List;
import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @Date 2022/8/4
 */
@Slf4j
public class MysqlQueryRunner extends BaseQueryRunner {

  public MysqlQueryRunner(DataSource dataSource, ApolloRefreshConfig apolloRefreshConfig, DataManage dataManage) {
    super(dataSource,apolloRefreshConfig,dataManage);
  }


  @Override
  protected String getDataFromResultSet(ResultSet resultSet, int columnIndex, String columnType, QueryContext queryContext) throws SQLException {
    List<String> specialProcessNumberTypeList = Lists.newArrayList("double", "float", "decimal", "bigint", "integer", "int", "mediumint");
    int typeCode = queryContext.getTypeCode();

    boolean flag = false;
    for (String s : specialProcessNumberTypeList) {
      if (columnType.startsWith(s)) {
        flag = true;
        break;
      }
    }

    // todo 后续报表下载ListTableDownloadJob 支持对比下载，if && operateTypeCode == OperateType.QUERY.getCode() 需要去掉
    if (QueryType.QUERY_REPORT.getCode() == typeCode) {
      if (flag) {
        return formatValueForReportModule(resultSet.getString(columnIndex), true);
      } else {
        return formatValueForReportModule(resultSet.getObject(columnIndex), false);
      }
    } else {
      if (flag) {
        return formatValue(resultSet.getString(columnIndex));
      } else {
        return formatValue(resultSet.getObject(columnIndex));
      }
    }
  }

  private static String formatValueForReportModule(Object o, boolean flag) {
    if (o == null) {
      return Constant.REPORT_NULL_REPLACE;
    }

    if (o instanceof byte[]) {
      return StringUtil.formatHexDump((byte[]) o);
    }

    // process number type, like "20.0000700" to "20.00007", or "20.0" to "20"
    String number = o.toString();
    try {
      if (flag) {
        //
        if (number.contains("E")) {
          number = Double.parseDouble(number) + "";
          if (number.contains("E")) {
            number = new BigDecimal(number).toPlainString();
          }
        }
        if (number.contains(".")) {
          char[] numberCharArray = number.toCharArray();

          int index = 0;
          for (index = numberCharArray.length - 1; index > -1; index--) {
            if (numberCharArray[index] == '0') {
              continue;
            } else if (numberCharArray[index] == '.') {
              index--;
              break;
            } else {
              break;
            }
          }
          number = number.substring(0, index + 1);
        }
      }
    } catch (Exception e) {
      log.error("formatValueForReportModule happen error", e);
      return number;
    }
    return number;
  }



  @Override
  void beforeDoExecution(QueryContext queryContext, Statement statement) throws SQLException {
    statement.setQueryTimeout(apolloRefreshConfig.getStatementQueryTimeoutSeconds());
  }

  @Override
  void afterDoExecution(QueryContext queryContext, Statement statement) throws SQLException {

  }
}
