package com.bestpay.bigdata.bi.backend.query.runner;


import static com.bestpay.bigdata.bi.common.common.Constant.REDIS_BIGDATA_BI_QUERY_PROGRESS_INFO_MAP_KEY;
import static com.bestpay.bigdata.bi.common.enums.CodeEnum.SQL_OPERATION_NOT_PERMIT;

import com.bestpay.bigdata.bi.common.execution.DataManage;
import com.bestpay.bigdata.bi.common.api.RedisBaseService;
import com.bestpay.bigdata.bi.common.api.RedisService;
import com.bestpay.bigdata.bi.common.entity.QueryContext;
import com.bestpay.bigdata.bi.backend.util.ApplicationContextHelper;
import com.bestpay.bigdata.bi.common.config.ApolloRefreshConfig;
import com.bestpay.bigdata.bi.common.exception.QueryExecutionException;
import com.bestpay.bigdata.bi.common.util.StringUtil;
import com.facebook.presto.jdbc.PrestoArray;
import com.facebook.presto.jdbc.PrestoResultSet;
import com.facebook.presto.jdbc.PrestoStatement;
import com.facebook.presto.jdbc.QueryStats;
import java.io.Serializable;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.function.Consumer;
import javax.sql.DataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.dbcp2.DelegatingStatement;

/**
 * The type Presto query runner.
 *
 * <AUTHOR> Xiaobing
 * @date 2021 /11/27 16:46
 */
@Slf4j
public class PrestoQueryRunner extends BaseQueryRunner {

    /**
     * Instantiates a new Presto query runner.
     *
     * @param dataSource the data source
     */
    public PrestoQueryRunner(DataSource dataSource, ApolloRefreshConfig apolloRefreshConfig, DataManage dataManage) {
        super(dataSource,apolloRefreshConfig,dataManage);
    }


    @Override
    void beforeDoExecution(QueryContext queryContext, Statement statement) throws SQLException {
        PrestoStatement prestoStatement = getConcreteStatement(statement,PrestoStatement.class);
        if (prestoStatement != null) {
            PrestoProgressMonitor prestoProgressMonitor = new PrestoProgressMonitor(queryContext);
            prestoStatement.setProgressMonitor(prestoProgressMonitor);
        }
        prestoStatement.setQueryTimeout(super.apolloRefreshConfig.getStatementQueryTimeoutSeconds());
        try{
            log.info("ready to set presto runtime user : {}" + queryContext.getUsername());
            String setUser = "runtime_user_if_config" + "=" + queryContext.getUsername();
            prestoStatement.getConnection().setClientInfo("ClientInfo", setUser);
            String preSql = "SET SESSION runtime_user_if_config=" + "'" + queryContext.getUsername() + "'";
            prestoStatement.execute(preSql);
        }catch (Exception e) {
            log.error("set Presto runtime_user_if_config error, message: {}", e.getMessage());
        }
        log.info("set Presto queryTimeout {}'s ",prestoStatement.getQueryTimeout());
    }

    @Deprecated
    private PrestoStatement getPrestoStatement(Statement statement) {
        if (statement instanceof DelegatingStatement) {
            Statement delegate = ((DelegatingStatement) statement).getDelegate();
            do {
                delegate = ((DelegatingStatement) delegate).getDelegate();
            } while (delegate instanceof DelegatingStatement);
            if (delegate instanceof PrestoStatement) {
                return (PrestoStatement) delegate;
            }
        }
        return null;
    }


    /**
     * Do execute data query language(DQL).
     *
     * @param queryContext the query context
     * @throws SQLException the sql exception
     */
    @Override
    protected void doExecuteDql(QueryContext queryContext) throws SQLException {
        PrestoStatement prestoStatement = getConcreteStatement(super.statement,PrestoStatement.class);

        try (PrestoResultSet rs = (PrestoResultSet) prestoStatement.executeQuery(queryContext.getSql())) {
            String prestoQueryId = rs.getQueryId();
            log.info("DQL query of [{}] has finished, Presto query id : {} !", queryContext.getQueryId(),
                prestoQueryId);
            queryContext.setEngineQueryId(prestoQueryId);
            processQueryResult(queryContext, rs);
        }
    }

    @Override
    public void doExecuteNoneDql(QueryContext queryContext) throws SQLException {
        throw new QueryExecutionException(SQL_OPERATION_NOT_PERMIT);
    }

    @Override
    void afterDoExecution(QueryContext queryContext, Statement statement) {
        PrestoStatement prestoStatement = getConcreteStatement(statement,PrestoStatement.class);
        if (prestoStatement != null) {
            prestoStatement.clearProgressMonitor();
        }
    }

    @Override
    public String formatValue(Object obj) {
        String result = super.formatValue(obj);
        if (obj instanceof PrestoArray) {
            result = StringUtil.convertPrestoArrayToString(obj);
        }
        return result;
    }

    private static class PrestoProgressMonitor implements Consumer<QueryStats>, Serializable {

        private final String queryId;
        private final RedisService redisService;

        /**
         * Instantiates a new Presto progress monitor.
         *
         * @param qc the qc
         */
        public PrestoProgressMonitor(QueryContext qc) {
            this.queryId = qc.getQueryId();
            this.redisService = ApplicationContextHelper.getBean(RedisBaseService.class);
        }

        @Override
        public synchronized void accept(QueryStats queryStats) {
            String process = "0";
            if (queryStats.isScheduled() && queryStats.getTotalSplits() != 0) {
                process = Integer.toString(queryStats.getCompletedSplits() * 100 / queryStats.getTotalSplits());
            }

            log.debug("current process is: {}", process);
            redisService.setHashObj(REDIS_BIGDATA_BI_QUERY_PROGRESS_INFO_MAP_KEY, queryId, process);
        }
    }
}
