package com.bestpay.bigdata.bi.report.service.report;

import com.bestpay.bigdata.bi.database.dao.common.AppEmbedDO;
import com.bestpay.bigdata.bi.report.bean.embed.ShareParam;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */

@Component
public interface UrlGenerateService {

    String embedObjectUrl(AppEmbedDO embedDO);

    String embedObjectUrlParamKey(AppEmbedDO embedDO);

    String embedObjectUrlNoExpireParamKey(AppEmbedDO embedDO);

    String embedNewDataScreenUrl(AppEmbedDO embedDO);

    String embedObjectUrlNoExpireParamKey(AppEmbedDO embedDO, ShareParam shareParam);
}
