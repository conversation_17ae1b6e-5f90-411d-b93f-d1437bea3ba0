package com.bestpay.bigdata.bi.report.service.handler;

import com.bestpay.bigdata.bi.common.dto.dashboard.TableCardInfoDTO;
import com.bestpay.bigdata.bi.common.dto.dashboard.newcard.NewIndexCardDTO;
import com.bestpay.bigdata.bi.common.dto.report.ReportUuidGenerateUtil;
import com.bestpay.bigdata.bi.common.dto.report.component.CommonComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.enums.NewCardTypeEnum;
import com.bestpay.bigdata.bi.common.util.StringUtil;
import com.bestpay.bigdata.bi.common.util.UserContextUtil;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardIndexTextCardService;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardIndexTextCardDO;
import com.bestpay.bigdata.bi.report.service.dashboard.DashboardTableCardService;
import com.bestpay.bigdata.bi.report.service.dashboard.ReportNewTableCardDbService;
import com.google.common.collect.Maps;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * @author:gaodingsong
 * @description:
 * @createTime:2024/4/17 15:08
 * @version:1.0
 */
@Service("indexText")
public class IndexTextDashboardHandler implements DashboardHandler{

    @Resource
    private DashboardIndexTextCardService indexTextCardService;

    @Resource
    private DashboardTableCardService dashboardTableCardService;

    @Resource
    private ReportNewTableCardDbService reportNewTableCardDbService;

    @Override
    public Long copyAndInsert(Long oldDashboard,
        String oldCode,
        Long cardId,
        Long newDashboardId,
        Map<String, String> cardCodeMap,
        Map<Long, Long> embedTextIdMap,
        String cardName,
        String indexType) {

        TableCardInfoDTO cardInfoDTO = dashboardTableCardService.getIndexTextCard(oldDashboard,
            oldCode, cardId, NewCardTypeEnum.INDEX_TEXT.getCode(), indexType);

        // 指标卡片
        NewIndexCardDTO reportCard = (NewIndexCardDTO) cardInfoDTO.getCardInfo();
        if(reportCard.getIndexInfo()!=null && StringUtil.isNotEmpty(cardName)) {
            reportCard.getIndexInfo().setName(cardName);
        }

        Map<String, String> computerUuidMap = Maps.newHashMap();

        reportCard.getCountFiledList().forEach(compute -> computerUuidMap.put(compute.getUuid(), ReportUuidGenerateUtil.generateReportComputeUuid()));
        reportCard.getDragResult().forEach(drag -> {
            if (drag.getEnName().startsWith("compute_")) {
                computerUuidMap.put(drag.getUuid(), ReportUuidGenerateUtil.generateReportComputeUuid());
            } else {
                computerUuidMap.put(drag.getUuid(), drag.getUuid());
            }
        });
        reportCard.getFilterdragResult().forEach(filter -> {
            if (filter.getEnName().startsWith("compute_")) {
                computerUuidMap.put(filter.getUuid(), ReportUuidGenerateUtil.generateReportComputeUuid());
            } else {
                computerUuidMap.put(filter.getUuid(), filter.getUuid());
            }
        });

        /**
         * 需要重新生成config uuid
         * 用于找到自定义配置列宽的config uuid
         */
        Map<String, String> configUuidMap = Maps.newHashMap();

        // 重新生成计算字段uuid
        CommonComponentPropertyDTO.setUuid(reportCard.getCountFiledList(), computerUuidMap, configUuidMap);
        // 重新生成过滤uuid
        CommonComponentPropertyDTO.setUuid(reportCard.getFilterdragResult(), computerUuidMap, configUuidMap);
        // 重新生成指标uuid
        CommonComponentPropertyDTO.setUuid(reportCard.getDragResult(), computerUuidMap, configUuidMap);

        DashboardIndexTextCardDO indexTextCardDO
            = reportNewTableCardDbService.toIndexTextCardDO(newDashboardId, UserContextUtil.get(), reportCard, indexType);

        indexTextCardDO.setIndexCardType(indexType);
        Long newCardId = indexTextCardService.insert(indexTextCardDO);
        return newCardId;
    }
}
