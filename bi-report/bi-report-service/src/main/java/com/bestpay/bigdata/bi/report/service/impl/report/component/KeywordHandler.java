package com.bestpay.bigdata.bi.report.service.impl.report.component;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.dto.report.ColumnPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.HighFunction;
import com.bestpay.bigdata.bi.common.dto.report.component.KeywordComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.enums.StatusCodeEnum;
import com.bestpay.bigdata.bi.common.response.ReportDetailVO;
import com.bestpay.bigdata.bi.common.util.UserContextUtil;
import com.bestpay.bigdata.bi.database.api.report.component.NewReportKeywordService;
import com.bestpay.bigdata.bi.database.dao.report.component.NewReportKeywordDO;
import com.bestpay.bigdata.bi.report.request.report.UpdateReportRequest;
import com.google.common.collect.Lists;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class KeywordHandler implements BaseHandler {

  @Resource
  private NewReportKeywordService keywordService;

  @Override
  public void delete(Long reportId,String resourceType,String componentType) {
    NewReportKeywordDO keywordDO = new NewReportKeywordDO();
    keywordDO.setReportId(reportId);
    keywordDO.setStatusCode(StatusCodeEnum.DELETE.getCode());
    keywordDO.setComponentType(componentType);
    keywordDO.setResourceType(resourceType);
    keywordService.update(keywordDO);
  }

  @Override
  public void store(UpdateReportRequest reportRequest, Long reportId,boolean isGenerateUUid) {
    if(Objects.isNull(reportRequest.getKeywordList())){
      return;
    }
    List<ColumnPropertyDTO> conditionColumnList
        = handlerColumn(reportRequest.getKeywordList(),isGenerateUUid);

    if(CollUtil.isNotEmpty(conditionColumnList)){
      List<NewReportKeywordDO> keywordDOS = Lists.newArrayList();
      for (ColumnPropertyDTO keywordDto : conditionColumnList) {
        NewReportKeywordDO keywordDO = new NewReportKeywordDO();
        BeanUtils.copyProperties(keywordDto, keywordDO);
        keywordDO.setFieldId(keywordDto.getId());
        keywordDO.setStatusCode(StatusCodeEnum.ONLINE.getCode());
        keywordDO.setHighFunctions(JSONUtil.toJsonStr(keywordDto.getHighFunctions()));
        keywordDO.setReportId(reportId);
        keywordDO.setCreatedAt(new Date());
        keywordDO.setUpdatedAt(new Date());
        keywordDO.setCreatedBy(UserContextUtil.getUserInfo().getEmail());
        keywordDO.setUpdatedBy(UserContextUtil.getUserInfo().getEmail());
        keywordDO.setResourceType(reportRequest.getResourceType());
        keywordDO.setComponentType(reportRequest.getComponentType());
        keywordDOS.add(keywordDO);
      }

      keywordService.insert(keywordDOS);
    }
  }

  @Override
  public void query(Long reportId, ReportDetailVO detailVO) {
    List<NewReportKeywordDO> keywordDOS = keywordService.findByReportId(reportId,detailVO.getResourceType(),detailVO.getComponentType());
    List<KeywordComponentPropertyDTO> responses = Lists.newArrayList();
    if (CollUtil.isNotEmpty(keywordDOS)) {
      for (NewReportKeywordDO keywordDO : keywordDOS) {
        KeywordComponentPropertyDTO response = new KeywordComponentPropertyDTO();
        BeanUtils.copyProperties(keywordDO, response);
        response.setId(keywordDO.getFieldId());
        response.setHighFunctions(
            JSONUtil.toList(keywordDO.getHighFunctions(), HighFunction.class));
        responses.add(response);
      }
    }

    detailVO.setKeywordList(responses);
  }

}
