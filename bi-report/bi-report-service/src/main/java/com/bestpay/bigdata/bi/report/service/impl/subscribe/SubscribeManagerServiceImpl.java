package com.bestpay.bigdata.bi.report.service.impl.subscribe;

import static com.bestpay.bigdata.bi.common.enums.NewCardTypeEnum.FILTER;
import static com.bestpay.bigdata.bi.common.enums.NewCardTypeEnum.TAB;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.bestpay.bigdata.bi.common.bean.AiPlusUserSearchRequest;
import com.bestpay.bigdata.bi.common.bean.UserInfoRequest;
import com.bestpay.bigdata.bi.common.dto.report.ColumnPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.ReportScreeningConditionDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.ConditionComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.enums.FieldType;
import com.bestpay.bigdata.bi.common.enums.StatusCodeEnum;
import com.bestpay.bigdata.bi.common.error.ReportErrorCode;
import com.bestpay.bigdata.bi.common.exception.BiException;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import com.bestpay.bigdata.bi.common.response.PageQueryVO;
import com.bestpay.bigdata.bi.common.response.ReportDetailVO;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.common.util.StringUtil;
import com.bestpay.bigdata.bi.common.util.UserContextUtil;
import com.bestpay.bigdata.bi.database.api.common.DatePickerDAOService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardCardService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardDaoService;
import com.bestpay.bigdata.bi.database.api.dashboard.DashboardFilterCardService;
import com.bestpay.bigdata.bi.database.api.report.component.NewReportService;
import com.bestpay.bigdata.bi.database.api.subscribe.ObjectSubscribeLogService;
import com.bestpay.bigdata.bi.database.api.subscribe.ObjectSubscribeService;
import com.bestpay.bigdata.bi.database.bean.dashboard.Dashboard;
import com.bestpay.bigdata.bi.database.bean.report.ReportQueryDTO;
import com.bestpay.bigdata.bi.database.bean.subscribe.ObjectSubScribeDO;
import com.bestpay.bigdata.bi.database.bean.subscribe.ObjectSubScribeDTO;
import com.bestpay.bigdata.bi.database.bean.subscribe.ObjectSubScribeLog;
import com.bestpay.bigdata.bi.database.bean.subscribe.QuerySubLogDTO;
import com.bestpay.bigdata.bi.database.bean.subscribe.SendLogDO;
import com.bestpay.bigdata.bi.database.bean.subscribe.SubDTO;
import com.bestpay.bigdata.bi.database.dao.common.DatePickerConfigDO;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardCardDO;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardFilterCardDO;
import com.bestpay.bigdata.bi.database.dao.report.NewReportDO;
import com.bestpay.bigdata.bi.report.constant.CommonConstant;
import com.bestpay.bigdata.bi.report.enums.dashboard.FrequencyEnum;
import com.bestpay.bigdata.bi.report.enums.subscribe.SubscribeObjectTypeEnum;
import com.bestpay.bigdata.bi.report.request.dashboard.DashboardSubScribeRequest;
import com.bestpay.bigdata.bi.report.request.dashboard.DashboardSubScribeRequest.FilterConfig;
import com.bestpay.bigdata.bi.report.request.subscribe.IdRequest;
import com.bestpay.bigdata.bi.report.request.subscribe.QueryStateRequest;
import com.bestpay.bigdata.bi.report.request.subscribe.SearchSendLogRequest;
import com.bestpay.bigdata.bi.report.request.subscribe.SearchSubRequest;
import com.bestpay.bigdata.bi.report.request.subscribe.SubCountRequest;
import com.bestpay.bigdata.bi.report.request.subscribe.UpdateTaskStatusRequest;
import com.bestpay.bigdata.bi.report.response.subscribe.SendDetailVO;
import com.bestpay.bigdata.bi.report.response.subscribe.SendLogDetailVO;
import com.bestpay.bigdata.bi.report.response.subscribe.SendLogVO;
import com.bestpay.bigdata.bi.report.response.subscribe.SubDetailVO;
import com.bestpay.bigdata.bi.report.response.subscribe.SubDetailVO.DashboardTab;
import com.bestpay.bigdata.bi.report.response.subscribe.SubObjectVO;
import com.bestpay.bigdata.bi.report.response.subscribe.SubStateDetailVo;
import com.bestpay.bigdata.bi.report.response.subscribe.SubVO;
import com.bestpay.bigdata.bi.report.schedule.subscribe.enums.SubscribeStatusEnums;
import com.bestpay.bigdata.bi.report.service.report.ReportUpdateService;
import com.bestpay.bigdata.bi.report.service.subscribe.SubscribeManagerService;
import com.bestpay.bigdata.bi.report.util.ProductProfileUtils;
import com.bestpay.bigdata.bi.report.util.ReportUtil;
import com.bestpay.bigdata.bi.thirdparty.api.AiPlusUserService;
import com.bestpay.bigdata.product.profile.ProductProfileClient;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class SubscribeManagerServiceImpl implements SubscribeManagerService {

  public static final String UNKNOWN = "unknown";
  public static final String WEEK_TIP = "每{0}";
  public static final String MONTH_TIP = "每月{0}日";
  public static final String HOUR_TIP = "{0}至{1}间隔{2}小时";

  private static final Map<Integer, String> WEEK_INFO = Maps.newHashMap();
  static {
    WEEK_INFO.put(1, "周日");
    WEEK_INFO.put(2, "周一");
    WEEK_INFO.put(3, "周二");
    WEEK_INFO.put(4, "周三");
    WEEK_INFO.put(5, "周四");
    WEEK_INFO.put(6, "周五");
    WEEK_INFO.put(7, "周六");
  }
  /**
   * 订阅发送日志查询近90天
   */
  public static final Integer QUERY_SUB_SEND_LOG_LAST_DAY = -90;

  private static final Logger log = LoggerFactory.getLogger(SubscribeManagerServiceImpl.class);
  @Resource
  private NewReportService reportService;

  @Resource
  private ReportUpdateService reportUpdateService;

  @Resource
  private AiPlusUserService userService;

  @Resource
  private DashboardDaoService dashboardDaoService;

  @Resource
  private DashboardCardService cardService;

  @Resource
  private ObjectSubscribeService objectSubscribeService;

  @Resource
  private ObjectSubscribeLogService subscribeLogService;

  @Resource
  private DatePickerDAOService pickerDAOService;

  @Resource
  private DashboardFilterCardService filterCardService;
  @Resource
  private ProductProfileClient productProfileClient;

  @Override
  public PageQueryVO<SubVO> taskList(SearchSubRequest request) {
    Integer pageNum = request.getPageNum();
    Integer pageSize = request.getPageSize();
    ObjectSubScribeDTO subScribeDTO = buildObjectSubScribeDTO(request);
    Page<SubVO> subVOPage = PageHelper.startPage(pageNum,pageSize);
    List<SubDTO> taskList =   objectSubscribeService.getSubscriptionTaskList(subScribeDTO);
    Map<String, UserInfo> userInfoMap = getUserInfoMap();
    List<SubVO> result = new ArrayList<>(taskList.size());
    for (SubDTO subDTO : taskList) {
      SubVO subVO =  new SubVO();
      BeanUtils.copyProperties(subDTO,subVO);
      String createdBy = subDTO.getCreatedBy();
      UserInfo userInfo = userInfoMap.get(createdBy);
      boolean flag = Objects.nonNull(userInfo);
      subVO.setOwnerEmail(createdBy);
      subVO.setOwner(flag ? userInfo.getAccount(): UNKNOWN);
      subVO.setOwnerCn(flag ? userInfo.getNickName(): UNKNOWN);
      subVO.setStartTime(buildPostFrequency(subVO.getPostFrequency(),subDTO.getStartTime()));
      result.add(subVO);
    }
    PageQueryVO<SubVO> pageQueryVO = new PageQueryVO<>();
    pageQueryVO.setPageNum(pageNum);
    pageQueryVO.setPageSize(pageSize);
    pageQueryVO.setTotalSize(subVOPage.getTotal());
    pageQueryVO.setData(result);
    return pageQueryVO;
  }

  /**
   * 根据频率和开始时间构建发送频率消息
   * @param frequency 频率
   * @param startTime 开始时间
   * @return 发送频率消息
   */
  private static String buildPostFrequency(String frequency,String startTime){
    String frequencyInfo;
    // 日。单次，周，月，小时
    String[] split = startTime.split(CommonConstant.COLON);

    if (FrequencyEnum.DAY.getCode().equals(frequency) || FrequencyEnum.ONE.getCode().equals(frequency)){
      // 如果是天 和一次。 则按照 直接返回时间
      frequencyInfo = basePostFrequency(split,CommonConstant.NUMBER_ONE,CommonConstant.NUMBER_TWO);
    }else if (FrequencyEnum.WEEK.getCode().equals(frequency)){
      String weekStr = split[CommonConstant.NUMBER_ZERO];
      int week = Integer.parseInt(weekStr);
      if (week == CommonConstant.NUMBER_ONE ){
        // 等于1 说明是周日
        weekStr = "日";
      }else {
        weekStr =  String.valueOf(getWeekInfo(week));
      }
      frequencyInfo  = MessageFormat.format(WEEK_TIP, weekStr) + basePostFrequency(split,CommonConstant.NUMBER_ONE,CommonConstant.NUMBER_TWO);
    }else if (FrequencyEnum.MONTH.getCode().equals(frequency)){
      // 如果是月 。直接返每月{0}日 + 时间
      String monthStr = split[CommonConstant.NUMBER_ZERO];
      frequencyInfo  = MessageFormat.format(MONTH_TIP, monthStr) + basePostFrequency(split,CommonConstant.NUMBER_ONE,CommonConstant.NUMBER_TWO);
    }else if (FrequencyEnum.HOUR.getCode().equals(frequency)){
      // 如果是小时  则按照 开始时间和结束时间+间隔
      startTime = startTime.replaceAll(CommonConstant.STRIKE_THROUGH,CommonConstant.COLON);
      split = startTime.split(CommonConstant.COLON);
      frequencyInfo  = MessageFormat.format(HOUR_TIP,
              basePostFrequency(split,CommonConstant.NUMBER_ZERO,CommonConstant.NUMBER_ONE),
              basePostFrequency(split,CommonConstant.NUMBER_TWO,CommonConstant.NUMBER_THREE),
              split[CommonConstant.NUMBER_FOUR]);
    }else {
      log.warn("未知频率类型: {}", frequency);
      // 为了程序不报错，给出默认值
      frequencyInfo = startTime;
    }
    return frequencyInfo;
  }

  /**
   * 根据入参，查询实际的周
   * @param week 周，1表示周日，2表示周一
   * @return 返回具体的周信息
   */
  private static String getWeekInfo(Integer week){
    if (week == null || week < CommonConstant.NUMBER_ONE || week > 7) {
      throw new BiException(ReportErrorCode.REPORT_INVALID_WEEKLY_INFO,"无效的周信息: " + week);
    }
    return WEEK_INFO.get(week);
  }

  /**
   * 构建基础的发送频率信息
   * @param split 分割数组
   * @param startIndex 开始索引
   * @param endIndex 结束索引
   * @return 发送频率信息
   */
  private static String basePostFrequency(String[] split,Integer startIndex,Integer endIndex){
    String frequencyInfo = "";
    if (split.length > endIndex) {
      // 安全校验
      frequencyInfo =  split[startIndex] + CommonConstant.COLON + split[endIndex];
    }
    return frequencyInfo;
  }

  private @NotNull Map<String, UserInfo> getUserInfoMap() {
    Map<String, UserInfo> map = new HashMap<>();
    List<UserInfo> userList = userService.getUserList(new UserInfoRequest());
    log.info("getUserInfoMap.userList:{}",JSONUtil.toJsonStr(userList));
    for (UserInfo userInfo : userList) {
      map.put(userInfo.getEmail(),userInfo);
    }
      return map;
  }




  @Override
  public List<SubObjectVO> objectList() {
    ObjectSubScribeDTO request = new ObjectSubScribeDTO();
    request.setStatusCodes( Stream.of(1,0).collect(Collectors.toList()));
    List<ObjectSubScribeDO> subScribeDOList = objectSubscribeService.queryByObjSubDTO(request);
    List<Long> reportIdList = new ArrayList<>();
    List<Long> dashboardIdList = new ArrayList<>();
    for (ObjectSubScribeDO subScribeDO : subScribeDOList) {
      if (SubscribeObjectTypeEnum.REPORT.getCode().equals(subScribeDO.getObjectType())){
        reportIdList.add(subScribeDO.getObjectId());
      }else {
        dashboardIdList.add(subScribeDO.getObjectId());
      }
    }

    List<SubObjectVO> list = new ArrayList<>();
    if (CollUtil.isNotEmpty(dashboardIdList)){
      List<Dashboard> dashboardDos =  dashboardDaoService.queryByIdList(dashboardIdList);
      Map<Long, Dashboard> dashboardDoMap = dashboardDos.stream().collect(Collectors.toMap(Dashboard::getId, Function.identity()));
      for (ObjectSubScribeDO subScribeDO : subScribeDOList) {
        if (SubscribeObjectTypeEnum.DASHBOARD.getCode().equals(subScribeDO.getObjectType())){
          Dashboard dashboardDo = dashboardDoMap.get(subScribeDO.getObjectId());
          if (Objects.nonNull(dashboardDo)){
            list.add(buildSubObjectVO(subScribeDO,dashboardDo.getName()));
          }
        }
      }
    }

    if (CollUtil.isNotEmpty(reportIdList)){
      ReportQueryDTO queryDTO = new ReportQueryDTO();
      queryDTO.setIdList(reportIdList);
      List<NewReportDO>reportList =  reportService.queryAll(queryDTO);
      Map<Long, NewReportDO> reportMap = reportList.stream().collect(Collectors.toMap(NewReportDO::getId, Function.identity()));
      for (ObjectSubScribeDO subScribeDO : subScribeDOList) {
        if (SubscribeObjectTypeEnum.REPORT.getCode().equals(subScribeDO.getObjectType())){
          NewReportDO report = reportMap.get(subScribeDO.getObjectId());
          if (Objects.nonNull(report)){
            list.add(buildSubObjectVO(subScribeDO,report.getReportName()));
          }
        }
      }
    }
    return list.stream().distinct().collect(Collectors.toList());
  }

  private SubObjectVO buildSubObjectVO(ObjectSubScribeDO subScribeDO,String objName){
    SubObjectVO vo = new SubObjectVO();
    vo.setObjectId(subScribeDO.getObjectId());
    vo.setObjectType(subScribeDO.getObjectType());
    vo.setObjectName(objName);
    return vo;

  }

  @Override
  public SubDetailVO detail(IdRequest request) {
    ObjectSubScribeDO objectSubScribeDO = objectSubscribeService.getById(request.getId());
    Preconditions.checkArgument(Objects.nonNull(objectSubScribeDO),"该任务不存在，请核对");

    SubDetailVO detailVO =new SubDetailVO();

    // 订阅的是报表
    if (SubscribeObjectTypeEnum.REPORT.getCode().equals(objectSubScribeDO.getObjectType())){
      Response<ReportDetailVO> report =
          reportUpdateService.queryReportTemplate(objectSubScribeDO.getObjectId());
      Preconditions.checkArgument(Objects.nonNull(report.getData()),"该订阅报表对象不存在，请核对");
      detailVO.setObjectName(report.getData().getReportName());
      detailVO.setConditionList(getConditionList(objectSubScribeDO)); // 报表筛选器配置
      // 数据集信息
      detailVO.setDatasetInfo(JSONUtil.toJsonStr(report.getData().getDatasetInfoList()));

      // 计算字段
      detailVO.setComputeColumnList(report.getData().getComputeColumnList());
    }else {

      // 订阅的是仪表板
      Dashboard dashboard = dashboardDaoService.getById(objectSubScribeDO.getObjectId());
      Preconditions.checkArgument(Objects.nonNull(dashboard),"该订阅仪表板对象不存在，请核对");

      detailVO.setObjectName(dashboard.getName());
      detailVO.setTypeCode(dashboard.getTypeCode());
      detailVO.setIsTabDashboard(dashboard.getIsTabDashboard());

      List<DashboardCardDO> tabCardList
          = cardService.findById(objectSubScribeDO.getObjectId(), TAB.getCode(), null);

      // 仪表板tab集合
      List<DashboardTab> tabList = Lists.newArrayList();
      for (DashboardCardDO dashboardCardDO : tabCardList) {
        DashboardTab tab = new DashboardTab();
        tab.setLabel(dashboard.getIsTabDashboard()==0 ? dashboard.getName() : dashboardCardDO.getName());
        tab.setValue(dashboardCardDO.getCardUniqueKey());
        tabList.add(tab);
      }
      detailVO.setTabList(tabList);

      Map<String, List<FilterConfig>> data = getAllFilterCardConfig(
          objectSubScribeDO);

      detailVO.setCurrentFilterConfigMap(data);

      // 仪表板筛选器配置
      detailVO.setFilterConfigMap(JSONUtil
          .toBean(objectSubScribeDO.getCondition(), Map.class));
    }

    BeanUtils.copyProperties(objectSubScribeDO,detailVO);
    detailVO.setSubscribeType(objectSubScribeDO.getSubType());
    detailVO.setTaskId(request.getId());
    detailVO.setObjectId(objectSubScribeDO.getObjectId());
    detailVO.setOwnerEmail(objectSubScribeDO.getCreatedBy());
    detailVO.setSubscribeContent(JSONUtil.toList(objectSubScribeDO.getSubscribeContent(), String.class));

    return detailVO;
  }

  @NotNull
  private Map<String, List<FilterConfig>> getAllFilterCardConfig(ObjectSubScribeDO objectSubScribeDO) {
    List<DashboardCardDO> filters = cardService
        .findById(objectSubScribeDO.getObjectId(), FILTER.getCode(), null);

    List<DashboardCardDO> tabs = cardService
        .findById(objectSubScribeDO.getObjectId(), TAB.getCode(), null);

    Map<String, List<FilterConfig>> data = Maps.newHashMap();
    for (DashboardCardDO tab : tabs) {
      for (DashboardCardDO filter : filters) {
        if (filter.getPid().equals(tab.getId())) {

          DashboardFilterCardDO filterCardDO = filterCardService.findById(
              filter.getCardId());

          FilterConfig filterCardDTO = new FilterConfig();
          filterCardDTO.setDatasetId(filterCardDO.getDatasetId());
          filterCardDTO.setCardCode(filter.getCardUniqueKey());
          filterCardDTO.setFields(
              JSONUtil.toBean(filterCardDO.getFields(), DashboardSubScribeRequest.Fields.class));

          List<FilterConfig> filterDTOs
              = data.getOrDefault(tab.getCardUniqueKey(), Lists.newArrayList());

          filterDTOs.add(filterCardDTO);

          data.putIfAbsent(tab.getCardUniqueKey(), filterDTOs);
        }
      }
    }
    return data;
  }

  private List<ConditionComponentPropertyDTO> getConditionList(ObjectSubScribeDO objectSubScribeDO) {
    List<ConditionComponentPropertyDTO> responses = Lists.newArrayList();
    List<ColumnPropertyDTO> conditionDTOs
        = JSONUtil.toList(objectSubScribeDO.getCondition(), ColumnPropertyDTO.class);

    if (CollUtil.isNotEmpty(conditionDTOs)) {
      for (ColumnPropertyDTO conditionDO : conditionDTOs) {
        ConditionComponentPropertyDTO response = new ConditionComponentPropertyDTO();
        BeanUtils.copyProperties(conditionDO, response);

        response.setScreeningCondition(getScreeningCondition(conditionDO));
        responses.add(response);
      }
    }

    return responses;
  }

  private ReportScreeningConditionDTO getScreeningCondition(ColumnPropertyDTO conditionDO) {
    if(conditionDO.getScreeningCondition() !=null) {
      ReportScreeningConditionDTO screeningCondition = new ReportScreeningConditionDTO();
      BeanUtils.copyProperties(conditionDO.getScreeningCondition(), screeningCondition);

      // 获取日期配置
      if (FieldType.DATETIME.name().equalsIgnoreCase(conditionDO.getShowTypeName())) {
        if (conditionDO.getScreeningCondition() != null
            && conditionDO.getScreeningCondition().getDatePickerId() != null) {

          DatePickerConfigDO configDO
              = pickerDAOService.select(conditionDO.getScreeningCondition().getDatePickerId());

          BeanUtils.copyProperties(configDO, screeningCondition);
          screeningCondition.setDefaultValues(
              JSONUtil.toList(configDO.getDefaultValue(), String.class));
        }
      }

      return screeningCondition;
    }

    return null;
  }

  @Override
  public PageQueryVO<SendLogVO> logList(SearchSendLogRequest request) {
    Page<SendLogDO> subVOPage = getObjectSubScribeLogs(request);
    List<SendLogDO> result = subVOPage.getResult();
    Map<String, UserInfo> userInfoMap = getUserInfoMap();
    List<SendLogVO> sendLogList = Lists.newArrayList();
    for (SendLogDO sendLogDO : result) {
      SendLogVO sendLogVO = new SendLogVO();
      BeanUtils.copyProperties(sendLogDO,sendLogVO);
      UserInfo userInfo = userInfoMap.get(sendLogDO.getCreatedBy());
      boolean existFlag = Objects.nonNull(userInfo);
      sendLogVO.setOwner(existFlag?userInfo.getAccount():UNKNOWN);
      sendLogVO.setOwnerCn(existFlag?userInfo.getNickName():UNKNOWN);
      sendLogVO.setStatusDesc(SubscribeStatusEnums.getNameByCode(sendLogDO.getStatus()));
      sendLogList.add(sendLogVO);
    }
    PageQueryVO<SendLogVO> pageQueryVO = new PageQueryVO<>();
    pageQueryVO.setPageSize(request.getPageSize());
    pageQueryVO.setPageNum(request.getPageNum());
    pageQueryVO.setTotalSize(subVOPage.getTotal());
    pageQueryVO.setData(sendLogList);

    return pageQueryVO;
  }



  @Override
  public SendDetailVO logListDetail(IdRequest request) {
    SendDetailVO detailVO = new SendDetailVO();

    ObjectSubScribeLog subScribeLog = subscribeLogService.getById(request.getId());
    Preconditions.checkArgument(Objects.nonNull(subScribeLog),"订阅发送详情为空");
    String sendDetail = subScribeLog.getSendDetail();
    List<SendLogDetailVO> list = JSONUtil.toList(sendDetail, SendLogDetailVO.class);
    int successCount = 0;
    List<SendLogDetailVO> failList = new ArrayList<>();
    for (SendLogDetailVO sendLogDetailVO : list) {
      if (sendLogDetailVO.getIsSuccess()){
        successCount++;
      }else {
        sendLogDetailVO.setEmail(getAccount(sendLogDetailVO.getEmail()));
        failList.add(sendLogDetailVO);
      }
    }

    detailVO.setStatus(subScribeLog.getState());
    detailVO.setMessage(subScribeLog.getMessage());
    detailVO.setTraceId(subScribeLog.getTraceId());
    detailVO.setSuccessCount(successCount);
    detailVO.setPartFail(failList);
    return detailVO;
  }

  @Override
  public Integer subCount(SubCountRequest request) {
    ObjectSubScribeDTO query = new ObjectSubScribeDTO();
    query.setObjectId(request.getObjectId());
    query.setObjectType(request.getObjectType());
    query.setStatusCodes(Stream.of(0,1).collect(Collectors.toList()));

    return objectSubscribeService.queryByObjSubDTO(query).size();
  }

  @Override
  public Integer updateTaskStatus(UpdateTaskStatusRequest request) {
    Integer status = request.getStatus();
    Preconditions.checkArgument(StrUtil.isNotBlank( StatusCodeEnum.getNameByCode(status)),"状态不存在,请核对");
    ObjectSubScribeDO byId = objectSubscribeService.getById(request.getId());
    Preconditions.checkArgument(Objects.nonNull(byId),"该订阅任务不存在");
    UserInfo userInfo = UserContextUtil.getUserInfo();
    if (!userInfo.getIsManager()){
      Preconditions.checkArgument(byId.getCreatedBy().equals(UserContextUtil.getUserInfo().getEmail()),"您不是责任人，无权限编辑");
    }

    ObjectSubScribeDO dashboardSubScribeDO = new ObjectSubScribeDO();
    dashboardSubScribeDO.setId(request.getId());
    dashboardSubScribeDO.setStatusCode(status);
    dashboardSubScribeDO.setUpdatedBy(UserContextUtil.getUserInfo().getEmail());
    dashboardSubScribeDO.setUpdatedAt(new Date());
    return objectSubscribeService.updateById(dashboardSubScribeDO);

  }


  private SubVO buildSubVO(ObjectSubScribeDO subScribeDO,String objectName ,Map<String, UserInfo> userInfoMap){
    String createdBy = subScribeDO.getCreatedBy();
    UserInfo userInfo = userInfoMap.get(createdBy);
    SubVO subVO = new SubVO();
    subVO.setTaskId(subScribeDO.getId());
    subVO.setTaskName(subScribeDO.getTaskName());
    subVO.setObjectType(subScribeDO.getObjectType());
    subVO.setObjectName(objectName);
    subVO.setSubscribeType(subScribeDO.getSubType());
    subVO.setPostFrequency(subScribeDO.getPostFrequency());
    subVO.setStartTime(dealStartTime(subScribeDO.getStartTime()));
    subVO.setCreatedAt(subScribeDO.getCreatedAt());
    subVO.setUpdatedAt(subScribeDO.getUpdatedAt());
    subVO.setStatusCode(subScribeDO.getStatusCode());
//    String createdBy = subScribeDO.getCreatedBy();
    subVO.setOwnerEmail(createdBy);
    subVO.setOwner(Objects.nonNull(userInfo)?userInfo.getAccount(): UNKNOWN);
    subVO.setOwnerCn(Objects.nonNull(userInfo)?userInfo.getNickName(): UNKNOWN);
    subVO.setSubCode(subScribeDO.getSubCode());
    subVO.setPostUrl(StrUtil.isNotBlank(subScribeDO.getPostUrl())? subScribeDO.getPostUrl() : "");
    return subVO;
  }


  private String dealStartTime(String startTime){
    return startTime.substring(startTime.length() - 5);
  }


  private ObjectSubScribeDTO buildObjectSubScribeDTO(SearchSubRequest request) {
    ObjectSubScribeDTO subScribeDTO = new ObjectSubScribeDTO();
    Integer statusCode = request.getStatusCode();
    List<Integer> statusCodes = new ArrayList<>(2);
    if (Objects.nonNull(statusCode)){
      statusCodes.add(statusCode);
    }else {
      statusCodes.add(1);
      statusCodes.add(0);
    }
    subScribeDTO.setStatusCodes(statusCodes);
    subScribeDTO.setCreatedBy(request.getOwnerEmail());
    subScribeDTO.setKeyword(ReportUtil.escapeLike(request.getKeyword()));
    return subScribeDTO;
  }

  private String getAccount(String email) {
    if (StrUtil.isBlank(email)) {
      return "unknown";
    }

    List<UserInfo> userList = userService.getUserList(AiPlusUserSearchRequest.builder()
        .ownerNames(Collections.singletonList(email)).build());

    if (CollUtil.isNotEmpty(userList)) {
      boolean flag = productProfileClient.boolValue(ProductProfileUtils.buildPlatformCondition());
      log.info("是否是终端公司：{}",flag);
      return flag?userList.get(0).getAccount():userList.get(0).getNickName();
    }

    return "unknown";
  }

  private PageQueryVO<SendLogVO> getSendLogVOPageQueryVO(SearchSendLogRequest request,
      Page<ObjectSubScribeLog> subVOPage,
      List<SendLogVO> data) {

    PageQueryVO<SendLogVO> pageQueryVO = new PageQueryVO<>();
    pageQueryVO.setPageSize(request.getPageSize());
    pageQueryVO.setPageNum(request.getPageNum());
    pageQueryVO.setTotalSize(subVOPage.getTotal());
    pageQueryVO.setData(data);
    return pageQueryVO;
  }

  private List<SendLogVO> getSendLogVOS(Page<ObjectSubScribeLog> subVOPage,
      List<NewReportDO> reports, List<Dashboard> dashboards) {
    Map<String, UserInfo> userInfoMap = getUserInfoMap();
    List<SendLogVO> collect = subVOPage.getResult().stream().map(s -> {
      UserInfo userInfo = userInfoMap.get(s.getCreatedBy());
      SendLogVO sendLogVO = new SendLogVO();
      sendLogVO.setId(s.getId());
      sendLogVO.setSubCode(s.getSubCode());
      sendLogVO.setTaskName(s.getTaskName());
      sendLogVO.setObjectType(s.getObjectType());
      sendLogVO.setSubscribeType(s.getSubType());
      sendLogVO.setSendTime(s.getCreatedAt());
      sendLogVO.setOwner(Objects.nonNull(userInfo)?userInfo.getAccount(): UNKNOWN);
      sendLogVO.setOwnerCn(Objects.nonNull(userInfo)?userInfo.getNickName(): UNKNOWN);
      sendLogVO.setStatus(s.getState());
      sendLogVO.setStatusDesc(SubscribeStatusEnums.getNameByCode(s.getState()));
      sendLogVO.setObjectName(getObjectName(reports, dashboards, s));

      return sendLogVO;
    }).collect(Collectors.toList());
    return collect;
  }

  private Page<SendLogDO> getObjectSubScribeLogs(SearchSendLogRequest request) {
    QuerySubLogDTO querySubLogDTO = getQuerySubLogDTO(
        request);

    Page<SendLogDO> subDOPage
        = PageHelper.startPage(request.getPageNum(), request.getPageSize());

    // 过滤90天内的数据
    querySubLogDTO.setBeginTime(DateUtil.offsetDay(new Date(), QUERY_SUB_SEND_LOG_LAST_DAY));

    subscribeLogService.queryLogList(querySubLogDTO);
    return subDOPage;
  }

  private QuerySubLogDTO getQuerySubLogDTO(SearchSendLogRequest request) {

    QuerySubLogDTO querySubLogDTO = new QuerySubLogDTO();
    BeanUtils.copyProperties(request,querySubLogDTO);

    if (StrUtil.isNotBlank(request.getStartTime())){
      Date date = DateUtil.parse(request.getStartTime(), "yyyy-MM-dd HH:mm:ss");
      querySubLogDTO.setStartTime(date);
    }

    if (StrUtil.isNotBlank(request.getEndTime())){
      Date date = DateUtil.parse(request.getEndTime(), "yyyy-MM-dd HH:mm:ss");
      querySubLogDTO.setEndTime(date);
    }
    querySubLogDTO.setKeyword(ReportUtil.escapeLike(request.getKeyword()));
    querySubLogDTO.setOwnerEmail(request.getOwnerEmail());
    return querySubLogDTO;
  }

  private List<NewReportDO> getReports(List<ObjectSubScribeLog> subScribeLogs) {
    List<Long> reportIds = subScribeLogs.stream()
        .filter(p->SubscribeObjectTypeEnum.REPORT.getCode().equalsIgnoreCase(p.getObjectType()))
        .map(p->p.getObjectId())
        .collect(Collectors.toList());

    if(CollUtil.isEmpty(reportIds)){
      return Lists.newArrayList();
    }

    ReportQueryDTO queryDTO = new ReportQueryDTO();
    queryDTO.setIdList(reportIds);

    List<NewReportDO> reports = reportService.queryAll(queryDTO);
    return reports;
  }

  private List<Dashboard> getDashboards(List<ObjectSubScribeLog> subScribeLogs) {
    List<Long> dashboardIds = subScribeLogs.stream()
        .filter(p->SubscribeObjectTypeEnum.DASHBOARD.getCode().equalsIgnoreCase(p.getObjectType()))
        .map(p->p.getObjectId())
        .collect(Collectors.toList());

    if(CollUtil.isEmpty(dashboardIds)){
      return Lists.newArrayList();
    }

    List<Dashboard> dashboards
        = dashboardDaoService.queryByIdList(dashboardIds);

    return dashboards;
  }

  private String getObjectName(List<NewReportDO> reports,
      List<Dashboard> dashboards,
      ObjectSubScribeLog log) {

    if (SubscribeObjectTypeEnum.REPORT
        .getCode().equalsIgnoreCase(log.getObjectType())) {

      for (NewReportDO report : reports) {
        if (log.getObjectId().equals(report.getId())) {
          return report.getReportName();
        }
      }
    }

    if (SubscribeObjectTypeEnum.DASHBOARD
        .getCode().equalsIgnoreCase(log.getObjectType())) {

      for (Dashboard dashboard : dashboards) {
        if (log.getObjectId().equals(dashboard.getId())) {
          return dashboard.getName();
        }
      }
    }

    return "unknown";
  }


  /**
   * 查询订阅任务状态
   * @param queryStateRequest
   * @return
   */
  public SubStateDetailVo queryState(QueryStateRequest queryStateRequest) {

    //判断subCode是否有效
    ObjectSubScribeDO subScribeDO = objectSubscribeService.getByCode(queryStateRequest.getSubCode());

    if (Objects.isNull(subScribeDO)) {
      throw new BusinessException("subCode " + queryStateRequest.getSubCode() + " 属于无效订阅任务ID");
    }

    //初始化订阅任务实例
    SubStateDetailVo subStateDetailVo = new SubStateDetailVo();
    subStateDetailVo.setSubCode(queryStateRequest.getSubCode());
    subStateDetailVo.setInstanceId(queryStateRequest.getInstanceId());
    subStateDetailVo.setState(SubscribeStatusEnums.none.getCode());
    subStateDetailVo.setMessage(SubscribeStatusEnums.none.getMsg());

    //查询log表数据库通过实例ID，查看详情，根据状态进行返回
    ObjectSubScribeLog objectSubScribeLog = subscribeLogService.queryByInstanceId(queryStateRequest.getInstanceId());
    if (Objects.nonNull(objectSubScribeLog)) {
      subStateDetailVo.setState(objectSubScribeLog.getState());
      subStateDetailVo.setMessage(objectSubScribeLog.getMessage());
    }
    return subStateDetailVo;
  }

}
