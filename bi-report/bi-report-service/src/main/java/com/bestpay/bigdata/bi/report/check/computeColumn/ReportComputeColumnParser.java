package com.bestpay.bigdata.bi.report.check.computeColumn;

import com.bestpay.bigdata.bi.common.dto.dataset.DatasetColumnConfigDTO;
import com.bestpay.bigdata.bi.common.dto.dataset.Dimension;
import com.bestpay.bigdata.bi.common.dto.dataset.DimensionRequest;
import com.bestpay.bigdata.bi.common.dto.report.component.ComputeComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.enums.DataSetFieldTypeEnum;
import com.bestpay.bigdata.bi.common.response.Response;
import com.bestpay.bigdata.bi.report.beforeSQL.util.ComponentComputerExpUtil;
import com.bestpay.bigdata.bi.report.cache.CacheHandleFactory;
import com.bestpay.bigdata.bi.report.cache.CommonCacheHandler;
import com.bestpay.bigdata.bi.report.cache.bean.DatasetCacheBean;
import com.bestpay.bigdata.bi.report.cache.enums.SceneEnums;
import com.bestpay.bigdata.bi.report.check.computeColumn.bean.ComputeColumnParseInfo;
import com.bestpay.bigdata.bi.report.request.dataset.DatasetColumnConfigRequest;
import com.bestpay.bigdata.bi.report.service.dataset.DatasetService;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @date 2024/5/6 15:18
 * @Description :
 **/
@Slf4j
@Component
public class ReportComputeColumnParser {

    @Resource
    private CacheHandleFactory cacheHandleFactory;

    @Resource
    private DatasetService datasetService;


    /**
     * 该方法的作用：一般用于将报表左边栏计算字段 进行中文字段的替换，替换成英文字段
     * @param computeColumnList 报表里面配置的计算字段，一般是左侧栏
     * @param datasetId 数据集id
     * @return ComputeColumnParseInfo
     */
    public ComputeColumnParseInfo computeColumnParseInfo(Long datasetId,
        List<DatasetColumnConfigDTO> datasetColumns,
        List<ComputeComponentPropertyDTO> computeColumnList) {

        ComputeColumnParseInfo computeColumnParseInfo = new ComputeColumnParseInfo();

        List<Dimension> dimensionList = Lists.newArrayList();
        List<DatasetColumnConfigDTO> paramList = Lists.newArrayList();
        List<DatasetColumnConfigDTO> computeList = Lists.newArrayList();

        log.info("datasetId {}", datasetId);

        DimensionRequest dimensionRequest = new DimensionRequest();
        dimensionRequest.setDatasetId(datasetId);

        CommonCacheHandler handleStrategy = cacheHandleFactory.getHandleStrategy(SceneEnums.DATA_SET);
        DatasetCacheBean<DimensionRequest, List<Dimension>> cacheBean =
                DatasetCacheBean.<DimensionRequest, List<Dimension>>builder()
                        .param(dimensionRequest)
                        .loader(req -> datasetService.getDimensionListV2(req))
                        .loaderName(datasetService.getClass().getName() + "getDimensionListV2")
                        .cacheKey(String.valueOf(datasetId))
                        .build();
        dimensionList = (List<Dimension>) handleStrategy.getCacheData(cacheBean);

        for (DatasetColumnConfigDTO config : datasetColumns) {
            // 过滤出来参数配置
            if (DataSetFieldTypeEnum.PARAM.equals(config.getFieldType())) {
                paramList.add(config);
            }
            // 过滤出来计算字段
            if (Objects.nonNull(config.getIsComputeField()) && config.getIsComputeField()) {
                computeList.add(config);
            }
        }

        // 数据集的计算列
        List<ComputeComponentPropertyDTO> datasetComputeColumnList
            = computeList.stream().map(p->p.coverDatasetColumnConfigDTO2ColumnProperty())
            .collect(Collectors.toList());

        // 把数据集的计算列和报表的计算列都加到这里
        computeColumnList.addAll(datasetComputeColumnList);

        Map<String, String> computeMap = ComponentComputerExpUtil.parseComputeColGenerateEnName(computeColumnList, dimensionList, paramList);
        String computeColumn = ComponentComputerExpUtil.parseComputeColCoreParam(computeMap, computeColumnList);

        //
        computeColumnParseInfo.setDimensionList(dimensionList);
        computeColumnParseInfo.setParsedCompute(computeColumn);
        computeColumnParseInfo.setComputeMap(computeMap);

        return computeColumnParseInfo;
    }



}
