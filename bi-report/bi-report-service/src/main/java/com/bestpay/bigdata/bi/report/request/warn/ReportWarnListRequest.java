package com.bestpay.bigdata.bi.report.request.warn;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("报表告警列表请求")
public class ReportWarnListRequest{

    @ApiModelProperty(value = "报表id")
    private String reportId;

    @ApiModelProperty(value = "告警源类型")
    private String warnSourceType;

    @ApiModelProperty(value = "责任人")
    private String ownerName;

    @ApiModelProperty(value = "状态")
    private Integer statusCode;

    @Deprecated
    /*
      已废弃的属性
      原因：需求不需要用该字段查询
      替代方案：无，后续将删除该字段
      版本：sprint62
      需求：AILAB-50205
     */
    private String orgCode;

    @ApiModelProperty(value = "关键字")
    private String keyWord;

    @ApiModelProperty(value = "当前页数")
    private Integer pageNum;

    @ApiModelProperty(value = "每页条数")
    private Integer pageSize;
}
