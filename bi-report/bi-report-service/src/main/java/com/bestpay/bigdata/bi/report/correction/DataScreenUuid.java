package com.bestpay.bigdata.bi.report.correction;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bestpay.bigdata.bi.common.dto.dataset.DatasetColumnConfigDTO;
import com.bestpay.bigdata.bi.common.dto.report.CustomColumnWidth;
import com.bestpay.bigdata.bi.common.dto.report.ReportSimpleColumn;
import com.bestpay.bigdata.bi.common.dto.report.ReportUuidGenerateUtil;
import com.bestpay.bigdata.bi.common.dto.report.TableConfiguration;
import com.bestpay.bigdata.bi.common.dto.warn.ReportWarnQueryDTO;
import com.bestpay.bigdata.bi.common.dto.warn.ReportWarnRuleDTO;
import com.bestpay.bigdata.bi.common.dto.warn.WarnConditionDTO;
import com.bestpay.bigdata.bi.common.enums.ReportResourceTypeEnum;
import com.bestpay.bigdata.bi.common.exception.BusinessException;
import com.bestpay.bigdata.bi.database.api.report.ReportWarnDAOService;
import com.bestpay.bigdata.bi.database.bean.report.ReportQueryDTO;
import com.bestpay.bigdata.bi.database.dao.datascreen.IndexTextComponentDO;
import com.bestpay.bigdata.bi.database.dao.datascreen.ReportComponentDO;
import com.bestpay.bigdata.bi.database.dao.report.NewReportDO;
import com.bestpay.bigdata.bi.database.dao.report.component.*;
import com.bestpay.bigdata.bi.database.dao.warn.ReportWarnDo;
import com.bestpay.bigdata.bi.database.dao.warn.ReportWarnRuleDo;
import com.bestpay.bigdata.bi.database.mapper.datascreen.IndexComponentMapper;
import com.bestpay.bigdata.bi.database.mapper.datascreen.ReportComponentMapper;
import com.bestpay.bigdata.bi.database.mapper.report.*;
import com.bestpay.bigdata.bi.database.mapper.warn.ReportWarnRuleMapper;
import com.bestpay.bigdata.bi.report.enums.report.ReportFieldEnum;
import com.bestpay.bigdata.bi.report.request.dataset.DatasetColumnConfigRequest;
import com.bestpay.bigdata.bi.report.service.dataset.DatasetService;
import com.bestpay.bigdata.bi.report.service.report.ReportUpdateService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Author: wybStart
 * @Date: 2025/6/10  19:18
 * @Description:
 */
@Slf4j
@Component
public class DataScreenUuid {


    @Resource
    private ReportComponentMapper newReportService;
    @Resource
    private DatasetService datasetService;
    @Resource
    private NewReportComputeMapper computeService;
    @Resource
    private NewReportDimensionMapper dimensionService;
    @Resource
    private NewReportContrastMapper contrastService;
    @Resource
    private NewReportIndexMapper indexService;
    @Resource
    private NewReportFilterMapper filterService;
    @Resource
    private NewReportConditionMapper conditionService;
    @Resource
    private NewReportKeywordMapper keywordService;
    @Resource
    private NewReportOrderMapper orderService;
    @Resource
    private NewReportStyleMapper styleService;
    @Resource
    private UuidTestMapper uuidTestMapper;
    @Resource
    private DataScreenUuid dataScreenUuid;
    @Resource
    private IndexComponentMapper indexComponentMapper;

    public void dataScreenUuid() {
        // 查询主表中所有的报表数据
        List<ReportComponentDO> newReportDOS = newReportService.findAll();

        /** key dataset id, value dataset_info */
        Map<String, List<DatasetColumnConfigDTO>> dataset_cache_map = new HashMap<>();

        log.info("数据大屏，总计需要订正的报表卡片个数: {}", newReportDOS.size());
        for (ReportComponentDO reportDO : newReportDOS) {
            Long datasetId = reportDO.getDatasetId();
            Long id = reportDO.getId();

            UuidTestDo uuidTestDo = new UuidTestDo();
            uuidTestDo.setObjectId(id);
            uuidTestDo.setObjectType(ReportResourceTypeEnum.DATASCREEN.name() + "_report");
            uuidTestDo.setInfoId(id);

            try {
                dataScreenUuid.processSingleReport(reportDO, id, datasetId, dataset_cache_map,
                        ReportResourceTypeEnum.DATASCREEN.name(), "report");
            } catch (Exception e) {
                uuidTestDo.setMessage(ExceptionUtil.stacktraceToString(e, 512));
                uuidTestMapper.insert(uuidTestDo);
            }
        }
        log.info("数据大屏报表卡片uuid订正完成...");


        // 查询主表中所有的指标数据
        List<IndexTextComponentDO> indexTextComponentDOS = indexComponentMapper.findAll();

        log.info("数据大屏，总计需要订正的指标卡片个数: {}", indexTextComponentDOS.size());
        for (IndexTextComponentDO reportDO : indexTextComponentDOS) {
            Long datasetId = reportDO.getDatasetId();
            Long id = reportDO.getId();

            UuidTestDo uuidTestDo = new UuidTestDo();
            uuidTestDo.setObjectId(id);
            uuidTestDo.setObjectType(ReportResourceTypeEnum.DATASCREEN.name() + "_index");
            uuidTestDo.setInfoId(id);

            try {
                dataScreenUuid.processSingleIndex(reportDO, id, datasetId, dataset_cache_map,
                        ReportResourceTypeEnum.DATASCREEN.name(), "index");
            } catch (Exception e) {
                uuidTestDo.setMessage(ExceptionUtil.stacktraceToString(e, 512));
                uuidTestMapper.insert(uuidTestDo);
            }
        }
        log.info("数据大屏指标卡片uuid订正完成...");
    }



    @Transactional(rollbackFor = Exception.class)
    public void processSingleIndex(IndexTextComponentDO reportDO, Long id, Long datasetId,
                                    Map<String, List<DatasetColumnConfigDTO>> dataset_cache_map,
                                    String resourcetype, String componenttype) {

        if (Objects.isNull(datasetId)) { // 只拖了卡片，未配置具体信息
            indexComponentMapper.insertShade(reportDO);
            return;
        }

        // 1. 构建数据来源信息
        List<DatasetColumnConfigDTO> datasetColumnList = null;
        if (dataset_cache_map.containsKey(datasetId + "")) {
            datasetColumnList = dataset_cache_map.get(datasetId + "");
        } else {
            DatasetColumnConfigRequest configRequest = new DatasetColumnConfigRequest();
            configRequest.setDatasetId(datasetId);
            datasetColumnList = datasetService.getColumnConfigList(configRequest).getData();
            dataset_cache_map.put(datasetId + "", datasetColumnList);
        }
        if (CollUtil.isEmpty(datasetColumnList)) {
            throw new BusinessException("当前数据集可能被删除 dataset id " + datasetId);
        }
        List<NewReportComputeDO> reportComputeList = computeService.findByReportId(id, resourcetype, componenttype);
        Map<String, String> reportComputeUuidMap = new HashMap<>();
        if (CollUtil.isNotEmpty(reportComputeList)) {
            for (NewReportComputeDO computeDO : reportComputeList) {
                String newUuid = ReportUuidGenerateUtil.generateReportComputeUuid();
                reportComputeUuidMap.put(computeDO.getUuid(), newUuid);
                computeDO.setUuid(newUuid);
            }
        }
        if (CollUtil.isNotEmpty(reportComputeList)) {
            computeService.insertShade(reportComputeList);
        }

        // 2. 解析report_structure
        List<ReportSimpleColumn> reportSimpleColumnList = JSONUtil.toList(reportDO.getReportStructure(), ReportSimpleColumn.class);
        if (judgeUuidRepeat(reportSimpleColumnList)) {
            throw new BusinessException("当前报表uuid 存在重复");
        }
        for (ReportSimpleColumn simpleColumn : reportSimpleColumnList) {
            if (CollUtil.isNotEmpty(simpleColumn.getChildColumnList())) {
                for (ReportSimpleColumn column : simpleColumn.getChildColumnList()) {
                    column.setUuid(matchNewUuid(column.getUuid(), column.getId(), column.getEnName(), column.getName(),
                            datasetColumnList, reportComputeList, reportComputeUuidMap));
                }
            } else {
                simpleColumn.setUuid(matchNewUuid(simpleColumn.getUuid(), simpleColumn.getId(), simpleColumn.getEnName(), simpleColumn.getName(),
                        datasetColumnList, reportComputeList, reportComputeUuidMap));
            }
        }
        reportDO.setReportStructure(JSONUtil.toJsonStr(reportSimpleColumnList));

        indexComponentMapper.insertShade(reportDO);

        /** key old uuid, value new config uuid */
        HashMap<String, String> uuidConfigMapForWarn = new HashMap<>();
        /** key old uuid, value new config measure uuid */
        HashMap<String, String> measureUuidMap = new HashMap<>();

        // 4. 子表 t_report_dimension_component
        List<NewReportDimensionDO> dimensionComponentList = dimensionService.findByReportId(id, resourcetype, componenttype);
        for (NewReportDimensionDO dimensionDO : dimensionComponentList) {
            dimensionDO.setConfigUuid(ReportUuidGenerateUtil.generateReportConfigUuid());

            String oldUuid = dimensionDO.getUuid();
            if (StringUtils.isNoneBlank(dimensionDO.getReportField())
                    && ReportFieldEnum.MEASURE.getCode().equals(dimensionDO.getReportField())) {
                dimensionDO.setUuid(ReportUuidGenerateUtil.generateReportMeasureUuid());
                measureUuidMap.put(oldUuid, dimensionDO.getUuid());
            } else {
                dimensionDO.setUuid(matchNewUuid(dimensionDO.getUuid(), dimensionDO.getFieldId(), dimensionDO.getEnName(), dimensionDO.getOriginalName(),
                        datasetColumnList, reportComputeList, reportComputeUuidMap));
            }
            uuidConfigMapForWarn.put(oldUuid, dimensionDO.getConfigUuid());
        }
        if (CollUtil.isNotEmpty(dimensionComponentList)) {
            dimensionService.insertShade(dimensionComponentList);
        }

        // 5. 子表 t_report_contrast_component
        List<NewReportContrastDO> contrastComponentList = contrastService.findByReportId(id, resourcetype, componenttype);
        for (NewReportContrastDO contrastDO : contrastComponentList) {
            contrastDO.setConfigUuid(ReportUuidGenerateUtil.generateReportConfigUuid());
            String oldUuid = contrastDO.getUuid();
            if (StringUtils.isNoneBlank(contrastDO.getReportField())
                    && ReportFieldEnum.MEASURE.getCode().equals(contrastDO.getReportField())) {
                contrastDO.setUuid(ReportUuidGenerateUtil.generateReportMeasureUuid());
                measureUuidMap.put(oldUuid, contrastDO.getUuid());
            } else {
                contrastDO.setUuid(matchNewUuid(contrastDO.getUuid(), contrastDO.getFieldId(), contrastDO.getEnName(), contrastDO.getOriginalName(),
                        datasetColumnList, reportComputeList, reportComputeUuidMap));
            }
            uuidConfigMapForWarn.put(oldUuid, contrastDO.getConfigUuid());
        }
        if (CollUtil.isNotEmpty(contrastComponentList)) {
            contrastService.insertShade(contrastComponentList);
        }

        // 6. 子表 t_report_index_component
        List<NewReportIndexDO> indexComponentList = indexService.findByReportId(id, resourcetype, componenttype);
        for (NewReportIndexDO indexDO : indexComponentList) {
            indexDO.setConfigUuid(ReportUuidGenerateUtil.generateReportConfigUuid());
            String oldUuid = indexDO.getUuid();
            indexDO.setUuid(matchNewUuid(indexDO.getUuid(), indexDO.getFieldId(), indexDO.getEnName(), indexDO.getOriginalName(),
                    datasetColumnList, reportComputeList, reportComputeUuidMap));
            uuidConfigMapForWarn.put(oldUuid, indexDO.getConfigUuid());
        }
        if (CollUtil.isNotEmpty(indexComponentList)) {
            indexService.insertShade(indexComponentList);
        }

        // 7. 子表 t_report_filter_component
        List<NewReportFilterDO> filterComponentList = filterService.findByReportId(id, resourcetype, componenttype);
        for (NewReportFilterDO filterDO : filterComponentList) {
            filterDO.setConfigUuid(ReportUuidGenerateUtil.generateReportConfigUuid());
            filterDO.setUuid(matchNewUuid(filterDO.getUuid(), filterDO.getFieldId(), filterDO.getEnName(), filterDO.getOriginalName(),
                    datasetColumnList, reportComputeList, reportComputeUuidMap));
        }
        if (CollUtil.isNotEmpty(filterComponentList)) {
            filterService.insertShade(filterComponentList);
        }

        // 8. 子表 t_report_condition_component
        List<NewReportConditionDO> conditionComponentList = conditionService.findByReportId(id, resourcetype, componenttype);
        for (NewReportConditionDO conditionDO : conditionComponentList) {
            conditionDO.setConfigUuid(ReportUuidGenerateUtil.generateReportConfigUuid());
            conditionDO.setUuid(matchNewUuid(conditionDO.getUuid(), conditionDO.getFieldId(), conditionDO.getEnName(), conditionDO.getOriginalName(),
                    datasetColumnList, reportComputeList, reportComputeUuidMap));
        }
        if (CollUtil.isNotEmpty(conditionComponentList)) {
            conditionService.insertShade(conditionComponentList);
        }

        // 9. 子表 t_report_keyword_component
        List<NewReportKeywordDO> keywordComponentList = keywordService.findByReportId(id, resourcetype, componenttype);
        for (NewReportKeywordDO keywordDO : keywordComponentList) {
            keywordDO.setConfigUuid(ReportUuidGenerateUtil.generateReportConfigUuid());
            keywordDO.setUuid(matchNewUuid(keywordDO.getUuid(), keywordDO.getFieldId(), keywordDO.getEnName(), keywordDO.getOriginalName(),
                    datasetColumnList, reportComputeList, reportComputeUuidMap));
        }
        if (CollUtil.isNotEmpty(keywordComponentList)) {
            keywordService.insertShade(keywordComponentList);
        }

        // 10. 子表 t_report_order_component
        List<NewReportOrderDO> orderComponentList = orderService.findByReportId(id, resourcetype, componenttype);
        for (NewReportOrderDO orderDO : orderComponentList) {
            orderDO.setConfigUuid(ReportUuidGenerateUtil.generateReportConfigUuid());
            orderDO.setUuid(matchNewUuid(orderDO.getUuid(), orderDO.getFieldId(), orderDO.getEnName(), orderDO.getOriginalName(),
                    datasetColumnList, reportComputeList, reportComputeUuidMap));
        }
        if (CollUtil.isNotEmpty(orderComponentList)) {
            orderService.insertShade(orderComponentList);
        }

        // 11. 子表 t_report_style_config
        NewReportStyleDO styleDO = styleService.findByReportId(id, resourcetype, componenttype);
        if (Objects.nonNull(styleDO) && StringUtils.isNoneBlank(styleDO.getTableConfiguration())) {
            TableConfiguration tableConfiguration = JSONUtil.toBean(styleDO.getTableConfiguration(),
                    TableConfiguration.class);
            if (tableConfiguration.getBasicFormat() != null
                    && CollUtil.isNotEmpty(tableConfiguration.getBasicFormat().getCustomColumnWidth())) {
                List<CustomColumnWidth> customColumnWidth = tableConfiguration.getBasicFormat().getCustomColumnWidth();
                for (CustomColumnWidth columnWidth : customColumnWidth) {
                    columnWidth.setConfigUuid(uuidConfigMapForWarn.get(columnWidth.getUuid()));
                    if (!ReportFieldEnum.MEASURE.getCode().equals(columnWidth.getReportField())) {
                        columnWidth.setUuid(matchNewUuid(columnWidth.getUuid(), null, null, columnWidth.getColumnName(),
                                datasetColumnList, reportComputeList, reportComputeUuidMap));
                    } else {
                        String newUuid = measureUuidMap.get(columnWidth.getUuid());
                        if (StringUtils.isNoneBlank(newUuid)) {
                            columnWidth.setUuid(newUuid);
                        } else {
                            throw new BusinessException("自定义宽度订正失败" + columnWidth.getUuid());
                        }
                    }
                }
                styleDO.setTableConfiguration(JSONUtil.toJsonStr(tableConfiguration));
            }
        }
        styleService.insertShadeStyle(styleDO);
    }



    @Transactional(rollbackFor = Exception.class)
    public void processSingleReport(ReportComponentDO reportDO, Long id, Long datasetId,
                                    Map<String, List<DatasetColumnConfigDTO>> dataset_cache_map,
                                    String resourcetype, String componenttype) {

        if (Objects.isNull(datasetId)) { // 只拖了卡片，未配置具体信息
            newReportService.insertShade(reportDO);
            return;
        }

        // 1. 构建数据来源信息
        List<DatasetColumnConfigDTO> datasetColumnList = null;
        if (dataset_cache_map.containsKey(datasetId + "")) {
            datasetColumnList = dataset_cache_map.get(datasetId + "");
        } else {
            DatasetColumnConfigRequest configRequest = new DatasetColumnConfigRequest();
            configRequest.setDatasetId(datasetId);
            datasetColumnList = datasetService.getColumnConfigList(configRequest).getData();
            dataset_cache_map.put(datasetId + "", datasetColumnList);
        }
        if (CollUtil.isEmpty(datasetColumnList)) {
            throw new BusinessException("当前数据集可能被删除 dataset id " + datasetId);
        }
        List<NewReportComputeDO> reportComputeList = computeService.findByReportId(id, resourcetype, componenttype);
        Map<String, String> reportComputeUuidMap = new HashMap<>();
        if (CollUtil.isNotEmpty(reportComputeList)) {
            for (NewReportComputeDO computeDO : reportComputeList) {
                String newUuid = ReportUuidGenerateUtil.generateReportComputeUuid();
                reportComputeUuidMap.put(computeDO.getUuid(), newUuid);
                computeDO.setUuid(newUuid);
            }
        }
        if (CollUtil.isNotEmpty(reportComputeList)) {
            computeService.insertShade(reportComputeList);
        }

        // 2. 解析report_structure
        List<ReportSimpleColumn> reportSimpleColumnList = JSONUtil.toList(reportDO.getReportStructure(), ReportSimpleColumn.class);
        if (judgeUuidRepeat(reportSimpleColumnList)) {
            throw new BusinessException("当前报表uuid 存在重复");
        }
        for (ReportSimpleColumn simpleColumn : reportSimpleColumnList) {
            if (CollUtil.isNotEmpty(simpleColumn.getChildColumnList())) {
                for (ReportSimpleColumn column : simpleColumn.getChildColumnList()) {
                    column.setUuid(matchNewUuid(column.getUuid(), column.getId(), column.getEnName(), column.getName(),
                            datasetColumnList, reportComputeList, reportComputeUuidMap));
                }
            } else {
                simpleColumn.setUuid(matchNewUuid(simpleColumn.getUuid(), simpleColumn.getId(), simpleColumn.getEnName(), simpleColumn.getName(),
                        datasetColumnList, reportComputeList, reportComputeUuidMap));
            }
        }
        reportDO.setReportStructure(JSONUtil.toJsonStr(reportSimpleColumnList));

        newReportService.insertShade(reportDO);

        /** key old uuid, value new config uuid */
        HashMap<String, String> uuidConfigMapForWarn = new HashMap<>();
        /** key old uuid, value new config measure uuid */
        HashMap<String, String> measureUuidMap = new HashMap<>();

        // 4. 子表 t_report_dimension_component
        List<NewReportDimensionDO> dimensionComponentList = dimensionService.findByReportId(id, resourcetype, componenttype);
        for (NewReportDimensionDO dimensionDO : dimensionComponentList) {
            dimensionDO.setConfigUuid(ReportUuidGenerateUtil.generateReportConfigUuid());

            String oldUuid = dimensionDO.getUuid();
            if (StringUtils.isNoneBlank(dimensionDO.getReportField())
                    && ReportFieldEnum.MEASURE.getCode().equals(dimensionDO.getReportField())) {
                dimensionDO.setUuid(ReportUuidGenerateUtil.generateReportMeasureUuid());
                measureUuidMap.put(oldUuid, dimensionDO.getUuid());
            } else {
                dimensionDO.setUuid(matchNewUuid(dimensionDO.getUuid(), dimensionDO.getFieldId(), dimensionDO.getEnName(), dimensionDO.getOriginalName(),
                        datasetColumnList, reportComputeList, reportComputeUuidMap));
            }
            uuidConfigMapForWarn.put(oldUuid, dimensionDO.getConfigUuid());
        }
        if (CollUtil.isNotEmpty(dimensionComponentList)) {
            dimensionService.insertShade(dimensionComponentList);
        }

        // 5. 子表 t_report_contrast_component
        List<NewReportContrastDO> contrastComponentList = contrastService.findByReportId(id, resourcetype, componenttype);
        for (NewReportContrastDO contrastDO : contrastComponentList) {
            contrastDO.setConfigUuid(ReportUuidGenerateUtil.generateReportConfigUuid());
            String oldUuid = contrastDO.getUuid();
            if (StringUtils.isNoneBlank(contrastDO.getReportField())
                    && ReportFieldEnum.MEASURE.getCode().equals(contrastDO.getReportField())) {
                contrastDO.setUuid(ReportUuidGenerateUtil.generateReportMeasureUuid());
                measureUuidMap.put(oldUuid, contrastDO.getUuid());
            } else {
                contrastDO.setUuid(matchNewUuid(contrastDO.getUuid(), contrastDO.getFieldId(), contrastDO.getEnName(), contrastDO.getOriginalName(),
                        datasetColumnList, reportComputeList, reportComputeUuidMap));
            }
            uuidConfigMapForWarn.put(oldUuid, contrastDO.getConfigUuid());
        }
        if (CollUtil.isNotEmpty(contrastComponentList)) {
            contrastService.insertShade(contrastComponentList);
        }

        // 6. 子表 t_report_index_component
        List<NewReportIndexDO> indexComponentList = indexService.findByReportId(id, resourcetype, componenttype);
        for (NewReportIndexDO indexDO : indexComponentList) {
            indexDO.setConfigUuid(ReportUuidGenerateUtil.generateReportConfigUuid());
            String oldUuid = indexDO.getUuid();
            indexDO.setUuid(matchNewUuid(indexDO.getUuid(), indexDO.getFieldId(), indexDO.getEnName(), indexDO.getOriginalName(),
                    datasetColumnList, reportComputeList, reportComputeUuidMap));
            uuidConfigMapForWarn.put(oldUuid, indexDO.getConfigUuid());
        }
        if (CollUtil.isNotEmpty(indexComponentList)) {
            indexService.insertShade(indexComponentList);
        }

        // 7. 子表 t_report_filter_component
        List<NewReportFilterDO> filterComponentList = filterService.findByReportId(id, resourcetype, componenttype);
        for (NewReportFilterDO filterDO : filterComponentList) {
            filterDO.setConfigUuid(ReportUuidGenerateUtil.generateReportConfigUuid());
            filterDO.setUuid(matchNewUuid(filterDO.getUuid(), filterDO.getFieldId(), filterDO.getEnName(), filterDO.getOriginalName(),
                    datasetColumnList, reportComputeList, reportComputeUuidMap));
        }
        if (CollUtil.isNotEmpty(filterComponentList)) {
            filterService.insertShade(filterComponentList);
        }

        // 8. 子表 t_report_condition_component
        List<NewReportConditionDO> conditionComponentList = conditionService.findByReportId(id, resourcetype, componenttype);
        for (NewReportConditionDO conditionDO : conditionComponentList) {
            conditionDO.setConfigUuid(ReportUuidGenerateUtil.generateReportConfigUuid());
            conditionDO.setUuid(matchNewUuid(conditionDO.getUuid(), conditionDO.getFieldId(), conditionDO.getEnName(), conditionDO.getOriginalName(),
                    datasetColumnList, reportComputeList, reportComputeUuidMap));
        }
        if (CollUtil.isNotEmpty(conditionComponentList)) {
            conditionService.insertShade(conditionComponentList);
        }

        // 9. 子表 t_report_keyword_component
        List<NewReportKeywordDO> keywordComponentList = keywordService.findByReportId(id, resourcetype, componenttype);
        for (NewReportKeywordDO keywordDO : keywordComponentList) {
            keywordDO.setConfigUuid(ReportUuidGenerateUtil.generateReportConfigUuid());
            keywordDO.setUuid(matchNewUuid(keywordDO.getUuid(), keywordDO.getFieldId(), keywordDO.getEnName(), keywordDO.getOriginalName(),
                    datasetColumnList, reportComputeList, reportComputeUuidMap));
        }
        if (CollUtil.isNotEmpty(keywordComponentList)) {
            keywordService.insertShade(keywordComponentList);
        }

        // 10. 子表 t_report_order_component
        List<NewReportOrderDO> orderComponentList = orderService.findByReportId(id, resourcetype, componenttype);
        for (NewReportOrderDO orderDO : orderComponentList) {
            orderDO.setConfigUuid(ReportUuidGenerateUtil.generateReportConfigUuid());
            orderDO.setUuid(matchNewUuid(orderDO.getUuid(), orderDO.getFieldId(), orderDO.getEnName(), orderDO.getOriginalName(),
                    datasetColumnList, reportComputeList, reportComputeUuidMap));
        }
        if (CollUtil.isNotEmpty(orderComponentList)) {
            orderService.insertShade(orderComponentList);
        }

        // 11. 子表 t_report_style_config
        NewReportStyleDO styleDO = styleService.findByReportId(id, resourcetype, componenttype);
        if (Objects.nonNull(styleDO) && StringUtils.isNoneBlank(styleDO.getTableConfiguration())) {
            TableConfiguration tableConfiguration = JSONUtil.toBean(styleDO.getTableConfiguration(),
                    TableConfiguration.class);
            if (tableConfiguration.getBasicFormat() != null
                    && CollUtil.isNotEmpty(tableConfiguration.getBasicFormat().getCustomColumnWidth())) {
                List<CustomColumnWidth> customColumnWidth = tableConfiguration.getBasicFormat().getCustomColumnWidth();
                for (CustomColumnWidth columnWidth : customColumnWidth) {
                    columnWidth.setConfigUuid(uuidConfigMapForWarn.get(columnWidth.getUuid()));
                    if (!ReportFieldEnum.MEASURE.getCode().equals(columnWidth.getReportField())) {
                        columnWidth.setUuid(matchNewUuid(columnWidth.getUuid(), null, null, columnWidth.getColumnName(),
                                datasetColumnList, reportComputeList, reportComputeUuidMap));
                    } else {
                        String newUuid = measureUuidMap.get(columnWidth.getUuid());
                        if (StringUtils.isNoneBlank(newUuid)) {
                            columnWidth.setUuid(newUuid);
                        } else {
                            throw new BusinessException("自定义宽度订正失败" + columnWidth.getUuid());
                        }
                    }
                }
                styleDO.setTableConfiguration(JSONUtil.toJsonStr(tableConfiguration));
            }
        }
        styleService.insertShadeStyle(styleDO);
    }





    private String matchNewUuid(String oldUuid, Long fieldId, String enName, String cnName,
                                List<DatasetColumnConfigDTO> datasetColumnList,
                                List<NewReportComputeDO> reportComputeList,
                                Map<String, String> reportComputeUuidMap) {
        // 优先处理匹配报表计算字段
        if (CollUtil.isNotEmpty(reportComputeList)) {
            if (StringUtils.isNoneBlank(oldUuid)) {
                String newUuid = reportComputeUuidMap.get(oldUuid);
                if (newUuid != null) {
                    return newUuid;
                }
            }
        }

        // 再匹配数据集配置的相关信息[ 基础字段、计算字段、参数 ]
        for (DatasetColumnConfigDTO configDTO : datasetColumnList) {
            if (fieldId != null) {
                if (fieldId.equals(configDTO.getId())) {
                    return configDTO.getUuid();
                }
            }
        }


        for (DatasetColumnConfigDTO configDTO : datasetColumnList) {
            if (StringUtils.isNotBlank(enName)) {
                if (enName.equals(configDTO.getEnName())) {
                    return configDTO.getUuid();
                }
            }
        }


        for (DatasetColumnConfigDTO configDTO : datasetColumnList) {
            if (StringUtils.isNotBlank(cnName)) {
                if (cnName.equals(configDTO.getName())) {
                    return configDTO.getUuid();
                }
                // 特殊情况 对于日期字段，比如数据集为 日期，但是报表会分成 日期(年), 日期(月)等，匹配的时候需要将(x)去掉进行匹配
                if (cnName.endsWith("(年)")
                        || cnName.endsWith("(月)")
                        || cnName.endsWith("(日)")
                        || cnName.endsWith("(周)")
                        || cnName.endsWith("(季)")
                        || cnName.endsWith("(时)")
                        || cnName.endsWith("(分)")
                        || cnName.endsWith("(秒)") ) {
                    String newCnName = cnName.substring(0, cnName.length() - 3);
                    if (newCnName.equals(configDTO.getName())) {
                        return configDTO.getUuid();
                    }
                }

            }
        }

        throw new BusinessException("匹配失败, 原始信息 oldUuid " + oldUuid + ", fieldId " + fieldId
                + ", enName " + enName + ", cnName " + cnName);
    }



    private boolean judgeUuidRepeat(List<ReportSimpleColumn> reportSimpleColumnList) {
        HashSet<String> uuidSet = new HashSet<>();

        for (ReportSimpleColumn reportSimpleColumn : reportSimpleColumnList) {
            if (uuidSet.contains(reportSimpleColumn.getUuid())) {
                return true;
            }
            uuidSet.add(reportSimpleColumn.getUuid());
        }
        return false;
    }








    @Transactional(rollbackFor = Exception.class)
    public void processSingleReportValidate(ReportComponentDO reportDO, Long id, Long datasetId, Map<String, List<DatasetColumnConfigDTO>> dataset_cache_map) {

        // 1. 构建数据来源信息
        List<DatasetColumnConfigDTO> datasetColumnList = null;
        if (dataset_cache_map.containsKey(datasetId + "")) {
            datasetColumnList = dataset_cache_map.get(datasetId + "");
        } else {
            DatasetColumnConfigRequest configRequest = new DatasetColumnConfigRequest();
            configRequest.setDatasetId(datasetId);
            datasetColumnList = datasetService.getColumnConfigList(configRequest).getData();
            dataset_cache_map.put(datasetId + "", datasetColumnList);
        }
        if (CollUtil.isEmpty(datasetColumnList)) {
            return;
        }
        List<NewReportComputeDO> reportComputeList = computeService.findByShadeReportId(id, ReportResourceTypeEnum.DATASCREEN.name(), "report");
        if (CollUtil.isNotEmpty(reportComputeList)) {
            for (NewReportComputeDO computeDO : reportComputeList) {
                // 这里验证没啥意义
                matchNewUuidValid(
                        computeDO.getUuid(),
                        computeDO.getFieldId(),
                        computeDO.getEnName(),
                        computeDO.getName(),
                        datasetColumnList, reportComputeList);
            }
        }

        // 2. 解析report_structure
        List<ReportSimpleColumn> reportSimpleColumnList = JSONUtil.toList(reportDO.getReportStructure(), ReportSimpleColumn.class);
        if (judgeUuidRepeat(reportSimpleColumnList)) {
            throw new BusinessException("当前报表uuid 存在重复");
        }
        for (ReportSimpleColumn simpleColumn : reportSimpleColumnList) {
            if (CollUtil.isNotEmpty(simpleColumn.getChildColumnList())) {
                for (ReportSimpleColumn column : simpleColumn.getChildColumnList()) {
                    matchNewUuidValid(column.getUuid(), column.getId(), column.getEnName(), column.getName(),
                            datasetColumnList, reportComputeList);
                }
            } else {
                matchNewUuidValid(simpleColumn.getUuid(), simpleColumn.getId(), simpleColumn.getEnName(), simpleColumn.getName(),
                        datasetColumnList, reportComputeList);
            }
        }


        /** value new uuid */
        HashSet<String> uuidSetForWarn = new HashSet<>();
        /** key old uuid, value new config uuid */
        HashSet<String> uuidConfigSetForWarn = new HashSet<>();
        /** value new config measure uuid */
        HashSet<String> measureUuidSet = new HashSet<>();

        // 4. 子表 t_report_dimension_component
        List<NewReportDimensionDO> dimensionComponentList = dimensionService.findByShadeReportId(id, ReportResourceTypeEnum.DATASCREEN.name(), "report");
        for (NewReportDimensionDO dimensionDO : dimensionComponentList) {
            if (StringUtils.isBlank(dimensionDO.getUuid()) || StringUtils.isBlank(dimensionDO.getConfigUuid())) {
                throw new BusinessException("当前报表维度表字段订正失败");
            }
            if (StringUtils.isNoneBlank(dimensionDO.getReportField())
                    && ReportFieldEnum.MEASURE.getCode().equals(dimensionDO.getReportField())) {
                measureUuidSet.add(dimensionDO.getUuid());
            } else {
                matchNewUuidValid(dimensionDO.getUuid(), dimensionDO.getFieldId(), dimensionDO.getEnName(), dimensionDO.getOriginalName(),
                        datasetColumnList, reportComputeList);
            }
            uuidSetForWarn.add(dimensionDO.getUuid());
            uuidConfigSetForWarn.add(dimensionDO.getConfigUuid());
        }

        // 5. 子表 t_report_contrast_component
        List<NewReportContrastDO> contrastComponentList = contrastService.findByShadeReportId(id, ReportResourceTypeEnum.DATASCREEN.name(), "report");
        for (NewReportContrastDO contrastDO : contrastComponentList) {
            if (StringUtils.isBlank(contrastDO.getUuid()) || StringUtils.isBlank(contrastDO.getConfigUuid())) {
                throw new BusinessException("当前报表对比表字段订正失败");
            }
            if (StringUtils.isNoneBlank(contrastDO.getReportField())
                    && ReportFieldEnum.MEASURE.getCode().equals(contrastDO.getReportField())) {
                measureUuidSet.add(contrastDO.getUuid());
            } else {
                matchNewUuidValid(contrastDO.getUuid(), contrastDO.getFieldId(), contrastDO.getEnName(), contrastDO.getOriginalName(),
                        datasetColumnList, reportComputeList);
            }
            uuidSetForWarn.add(contrastDO.getUuid());
            uuidConfigSetForWarn.add(contrastDO.getConfigUuid());
        }

        // 6. 子表 t_report_index_component
        List<NewReportIndexDO> indexComponentList = indexService.findByShadeReportId(id, ReportResourceTypeEnum.DATASCREEN.name(), "report");
        for (NewReportIndexDO indexDO : indexComponentList) {
            if (StringUtils.isBlank(indexDO.getUuid()) || StringUtils.isBlank(indexDO.getConfigUuid())) {
                throw new BusinessException("当前报表指标表字段订正失败");
            }
            matchNewUuidValid(indexDO.getUuid(), indexDO.getFieldId(), indexDO.getEnName(), indexDO.getOriginalName(), datasetColumnList, reportComputeList);
            uuidSetForWarn.add(indexDO.getUuid());
            uuidConfigSetForWarn.add(indexDO.getConfigUuid());
        }

        // 7. 子表 t_report_filter_component
        List<NewReportFilterDO> filterComponentList = filterService.findByShadeReportId(id, ReportResourceTypeEnum.DATASCREEN.name(), "report");
        for (NewReportFilterDO filterDO : filterComponentList) {
            if (StringUtils.isBlank(filterDO.getUuid()) || StringUtils.isBlank(filterDO.getConfigUuid())) {
                throw new BusinessException("当前报表过滤表字段订正失败");
            }
            matchNewUuidValid(filterDO.getUuid(), filterDO.getFieldId(), filterDO.getEnName(), filterDO.getOriginalName(),
                    datasetColumnList, reportComputeList);
        }

        // 8. 子表 t_report_condition_component
        List<NewReportConditionDO> conditionComponentList = conditionService.findByShadeReportId(id, ReportResourceTypeEnum.DATASCREEN.name(), "report");
        for (NewReportConditionDO conditionDO : conditionComponentList) {
            if (StringUtils.isBlank(conditionDO.getUuid()) || StringUtils.isBlank(conditionDO.getConfigUuid())) {
                throw new BusinessException("当前报表筛选器表字段订正失败");
            }
            matchNewUuidValid(conditionDO.getUuid(), conditionDO.getFieldId(), conditionDO.getEnName(), conditionDO.getOriginalName(),
                    datasetColumnList, reportComputeList);
        }

        // 9. 子表 t_report_keyword_component
        List<NewReportKeywordDO> keywordComponentList = keywordService.findByShadeReportId(id, ReportResourceTypeEnum.DATASCREEN.name(), "report");
        for (NewReportKeywordDO keywordDO : keywordComponentList) {
            if (StringUtils.isBlank(keywordDO.getUuid()) || StringUtils.isBlank(keywordDO.getConfigUuid())) {
                throw new BusinessException("当前报表关键字表字段订正失败");
            }
            matchNewUuidValid(keywordDO.getUuid(), keywordDO.getFieldId(), keywordDO.getEnName(), keywordDO.getOriginalName(),
                    datasetColumnList, reportComputeList);
        }

        // 10. 子表 t_report_order_component
        List<NewReportOrderDO> orderComponentList = orderService.findByShadeReportId(id, ReportResourceTypeEnum.DATASCREEN.name(), "report");
        for (NewReportOrderDO orderDO : orderComponentList) {
            if (StringUtils.isBlank(orderDO.getUuid()) || StringUtils.isBlank(orderDO.getConfigUuid())) {
                throw new BusinessException("当前报表排序表字段订正失败");
            }
            matchNewUuidValid(orderDO.getUuid(), orderDO.getFieldId(), orderDO.getEnName(), orderDO.getOriginalName(),
                    datasetColumnList, reportComputeList);
        }

        // 11. 子表 t_report_style_config
        NewReportStyleDO styleDO = styleService.findByShadeReportId(id, ReportResourceTypeEnum.DATASCREEN.name(), "report");
        if (Objects.nonNull(styleDO) && StringUtils.isNoneBlank(styleDO.getTableConfiguration())) {
            TableConfiguration tableConfiguration = JSONUtil.toBean(styleDO.getTableConfiguration(),
                    TableConfiguration.class);
            if (tableConfiguration.getBasicFormat() != null
                    && CollUtil.isNotEmpty(tableConfiguration.getBasicFormat().getCustomColumnWidth())) {
                List<CustomColumnWidth> customColumnWidth = tableConfiguration.getBasicFormat().getCustomColumnWidth();
                for (CustomColumnWidth columnWidth : customColumnWidth) {
                    if (StringUtils.isBlank(columnWidth.getUuid()) || StringUtils.isBlank(columnWidth.getConfigUuid())) {
                        throw new BusinessException("自定义表头宽度订正失败");
                    }

                    if (!uuidConfigSetForWarn.contains(columnWidth.getConfigUuid())) {
                        throw new BusinessException("自定义宽度订正失败" + columnWidth.getUuid());
                    }
                    if (!ReportFieldEnum.MEASURE.getCode().equals(columnWidth.getReportField())) {
                        matchNewUuidValid(columnWidth.getUuid(), null, null, columnWidth.getColumnName(),
                                datasetColumnList, reportComputeList);
                    } else {
                        if (!measureUuidSet.contains(columnWidth.getUuid())) {
                            throw new BusinessException("自定义宽度订正失败" + columnWidth.getUuid());
                        }
                    }
                }
            }
        }
    }



    private void matchNewUuidValid(String newUuid, Long fieldId, String enName, String cnName,
                                   List<DatasetColumnConfigDTO> datasetColumnList,
                                   List<NewReportComputeDO> reportComputeList) {
        if (StringUtils.isBlank(newUuid)) {
            throw new BusinessException("存在uuid订正失败, 原始信息 newUuid " + newUuid + ", fieldId " + fieldId
                    + ", enName " + enName + ", cnName " + cnName);
        }

        // 优先处理匹配报表计算字段
        if (CollUtil.isNotEmpty(reportComputeList)) {
            for (NewReportComputeDO computeDO : reportComputeList) {
                if (newUuid.equals(computeDO.getUuid())) {
                    if (fieldId != null && Objects.nonNull(computeDO.getFieldId())) {
                        if (fieldId.equals(computeDO.getFieldId())) {
                            return;
                        }
                    }

                    if (StringUtils.isNoneBlank(enName) && StringUtils.isNoneBlank(computeDO.getEnName())) {
                        if (enName.equals(computeDO.getEnName())) {
                            return;
                        }
                    }

                    if (StringUtils.isNoneBlank(cnName) && StringUtils.isNoneBlank(computeDO.getName())) {
                        if (cnName.equals(computeDO.getName())) {
                            return;
                        } else {
                            // 特殊情况 对于日期字段，比如数据集为 日期，但是报表会分成 日期(年), 日期(月)等，匹配的时候需要将(x)去掉进行匹配
                            if (cnName.endsWith("(年)")
                                    || cnName.endsWith("(月)")
                                    || cnName.endsWith("(日)")
                                    || cnName.endsWith("(周)")
                                    || cnName.endsWith("(季)")
                                    || cnName.endsWith("(时)")
                                    || cnName.endsWith("(分)")
                                    || cnName.endsWith("(秒)") ) {
                                String newCnName = cnName.substring(0, cnName.length() - 3);
                                if (newCnName.equals(computeDO.getName())) {
                                    return ;
                                }
                            }
                        }
                    }
                }
            }
        }

        // 再匹配数据集配置的相关信息[ 基础字段、计算字段、参数 ]
        for (DatasetColumnConfigDTO configDTO : datasetColumnList) {
            if (newUuid.equals(configDTO.getUuid())) {
                if (fieldId != null && Objects.nonNull(configDTO.getId())) {
                    if (fieldId.equals(configDTO.getId())) {
                        return;
                    }
                }
                if (StringUtils.isNoneBlank(enName) && StringUtils.isNoneBlank(configDTO.getEnName())) {
                    if (enName.equals(configDTO.getEnName())) {
                        return;
                    }
                }
                if (StringUtils.isNoneBlank(cnName) && StringUtils.isNoneBlank(configDTO.getName())) {
                    if (cnName.equals(configDTO.getName())) {
                        return;
                    } else {
                        // 特殊情况 对于日期字段，比如数据集为 日期，但是报表会分成 日期(年), 日期(月)等，匹配的时候需要将(x)去掉进行匹配
                        if (cnName.endsWith("(年)")
                                || cnName.endsWith("(月)")
                                || cnName.endsWith("(日)")
                                || cnName.endsWith("(周)")
                                || cnName.endsWith("(季)")
                                || cnName.endsWith("(时)")
                                || cnName.endsWith("(分)")
                                || cnName.endsWith("(秒)") ) {
                            String newCnName = cnName.substring(0, cnName.length() - 3);
                            if (newCnName.equals(configDTO.getName())) {
                                return ;
                            }
                        }
                    }
                }
            }
        }

        throw new BusinessException("匹配失败, 原始信息 newUuid " + newUuid + ", fieldId " + fieldId
                + ", enName " + enName + ", cnName " + cnName);
    }



    public void dataScreenUuidValid() {

        /** key dataset id, value dataset_info */
        Map<String, List<DatasetColumnConfigDTO>> dataset_cache_map = new HashMap<>();

        // 查询主表中所有的报表数据
        List<ReportComponentDO> newReportDOS = newReportService.findShadeAll();

        log.info("数据大屏，总计需要验证的报表个数: {}", newReportDOS.size());
        for (ReportComponentDO reportDO : newReportDOS) {
            Long datasetId = reportDO.getDatasetId();
            Long id = reportDO.getId();

            UuidTestDo uuidTestDo = new UuidTestDo();
            uuidTestDo.setObjectId(id);
            uuidTestDo.setObjectType(ReportResourceTypeEnum.DATASCREEN.name() + "_report_validate");
            uuidTestDo.setInfoId(datasetId);

            try {
                dataScreenUuid.processSingleReportValidate(reportDO, id, datasetId, dataset_cache_map);
            } catch (Exception e) {
                uuidTestDo.setMessage(ExceptionUtil.stacktraceToString(e, 2048));
                uuidTestMapper.insert(uuidTestDo);
            }
        }

        // 查询主表中所有的指标数据
        List<IndexTextComponentDO> indexTextDOS = indexComponentMapper.findShadeAll();

        log.info("数据大屏，总计需要订正的指标个数: {}", newReportDOS.size());
        for (IndexTextComponentDO reportDO : indexTextDOS) {
            Long datasetId = reportDO.getDatasetId();
            Long id = reportDO.getId();

            UuidTestDo uuidTestDo = new UuidTestDo();
            uuidTestDo.setObjectId(id);
            uuidTestDo.setObjectType("index_validate");
            uuidTestDo.setInfoId(datasetId);

            try {
                dataScreenUuid.processSingleIndexValidate(reportDO, id, datasetId, dataset_cache_map);
            } catch (Exception e) {
                uuidTestDo.setMessage(ExceptionUtil.stacktraceToString(e, 2048));
                uuidTestMapper.insert(uuidTestDo);
            }
        }

        log.info("数据大屏uuid验证完成...");
    }




    @Transactional(rollbackFor = Exception.class)
    public void processSingleIndexValidate(IndexTextComponentDO reportDO, Long id, Long datasetId, Map<String, List<DatasetColumnConfigDTO>> dataset_cache_map) {

        // 1. 构建数据来源信息
        List<DatasetColumnConfigDTO> datasetColumnList = null;
        if (dataset_cache_map.containsKey(datasetId + "")) {
            datasetColumnList = dataset_cache_map.get(datasetId + "");
        } else {
            DatasetColumnConfigRequest configRequest = new DatasetColumnConfigRequest();
            configRequest.setDatasetId(datasetId);
            datasetColumnList = datasetService.getColumnConfigList(configRequest).getData();
            dataset_cache_map.put(datasetId + "", datasetColumnList);
        }
        if (CollUtil.isEmpty(datasetColumnList)) {
            return;
        }
        List<NewReportComputeDO> reportComputeList = computeService.findByShadeReportId(id, ReportResourceTypeEnum.DATASCREEN.name(), "index");
        if (CollUtil.isNotEmpty(reportComputeList)) {
            for (NewReportComputeDO computeDO : reportComputeList) {
                // 这里验证没啥意义
                matchNewUuidValid(
                        computeDO.getUuid(),
                        computeDO.getFieldId(),
                        computeDO.getEnName(),
                        computeDO.getName(),
                        datasetColumnList, reportComputeList);
            }
        }

        // 2. 解析report_structure
        List<ReportSimpleColumn> reportSimpleColumnList = JSONUtil.toList(reportDO.getReportStructure(), ReportSimpleColumn.class);
        if (judgeUuidRepeat(reportSimpleColumnList)) {
            throw new BusinessException("当前报表uuid 存在重复");
        }
        for (ReportSimpleColumn simpleColumn : reportSimpleColumnList) {
            if (CollUtil.isNotEmpty(simpleColumn.getChildColumnList())) {
                for (ReportSimpleColumn column : simpleColumn.getChildColumnList()) {
                    matchNewUuidValid(column.getUuid(), column.getId(), column.getEnName(), column.getName(),
                            datasetColumnList, reportComputeList);
                }
            } else {
                matchNewUuidValid(simpleColumn.getUuid(), simpleColumn.getId(), simpleColumn.getEnName(), simpleColumn.getName(),
                        datasetColumnList, reportComputeList);
            }
        }


        /** value new uuid */
        HashSet<String> uuidSetForWarn = new HashSet<>();
        /** key old uuid, value new config uuid */
        HashSet<String> uuidConfigSetForWarn = new HashSet<>();
        /** value new config measure uuid */
        HashSet<String> measureUuidSet = new HashSet<>();

        // 4. 子表 t_report_dimension_component
        List<NewReportDimensionDO> dimensionComponentList = dimensionService.findByShadeReportId(id, ReportResourceTypeEnum.DATASCREEN.name(), "index");
        for (NewReportDimensionDO dimensionDO : dimensionComponentList) {
            if (StringUtils.isBlank(dimensionDO.getUuid()) || StringUtils.isBlank(dimensionDO.getConfigUuid())) {
                throw new BusinessException("当前报表维度表字段订正失败");
            }
            if (StringUtils.isNoneBlank(dimensionDO.getReportField())
                    && ReportFieldEnum.MEASURE.getCode().equals(dimensionDO.getReportField())) {
                measureUuidSet.add(dimensionDO.getUuid());
            } else {
                matchNewUuidValid(dimensionDO.getUuid(), dimensionDO.getFieldId(), dimensionDO.getEnName(), dimensionDO.getOriginalName(),
                        datasetColumnList, reportComputeList);
            }
            uuidSetForWarn.add(dimensionDO.getUuid());
            uuidConfigSetForWarn.add(dimensionDO.getConfigUuid());
        }

        // 5. 子表 t_report_contrast_component
        List<NewReportContrastDO> contrastComponentList = contrastService.findByShadeReportId(id, ReportResourceTypeEnum.DATASCREEN.name(), "index");
        for (NewReportContrastDO contrastDO : contrastComponentList) {
            if (StringUtils.isBlank(contrastDO.getUuid()) || StringUtils.isBlank(contrastDO.getConfigUuid())) {
                throw new BusinessException("当前报表对比表字段订正失败");
            }
            if (StringUtils.isNoneBlank(contrastDO.getReportField())
                    && ReportFieldEnum.MEASURE.getCode().equals(contrastDO.getReportField())) {
                measureUuidSet.add(contrastDO.getUuid());
            } else {
                matchNewUuidValid(contrastDO.getUuid(), contrastDO.getFieldId(), contrastDO.getEnName(), contrastDO.getOriginalName(),
                        datasetColumnList, reportComputeList);
            }
            uuidSetForWarn.add(contrastDO.getUuid());
            uuidConfigSetForWarn.add(contrastDO.getConfigUuid());
        }

        // 6. 子表 t_report_index_component
        List<NewReportIndexDO> indexComponentList = indexService.findByShadeReportId(id, ReportResourceTypeEnum.DATASCREEN.name(), "index");
        for (NewReportIndexDO indexDO : indexComponentList) {
            if (StringUtils.isBlank(indexDO.getUuid()) || StringUtils.isBlank(indexDO.getConfigUuid())) {
                throw new BusinessException("当前报表指标表字段订正失败");
            }
            matchNewUuidValid(indexDO.getUuid(), indexDO.getFieldId(), indexDO.getEnName(), indexDO.getOriginalName(), datasetColumnList, reportComputeList);
            uuidSetForWarn.add(indexDO.getUuid());
            uuidConfigSetForWarn.add(indexDO.getConfigUuid());
        }

        // 7. 子表 t_report_filter_component
        List<NewReportFilterDO> filterComponentList = filterService.findByShadeReportId(id, ReportResourceTypeEnum.DATASCREEN.name(), "index");
        for (NewReportFilterDO filterDO : filterComponentList) {
            if (StringUtils.isBlank(filterDO.getUuid()) || StringUtils.isBlank(filterDO.getConfigUuid())) {
                throw new BusinessException("当前报表过滤表字段订正失败");
            }
            matchNewUuidValid(filterDO.getUuid(), filterDO.getFieldId(), filterDO.getEnName(), filterDO.getOriginalName(),
                    datasetColumnList, reportComputeList);
        }

        // 8. 子表 t_report_condition_component
        List<NewReportConditionDO> conditionComponentList = conditionService.findByShadeReportId(id, ReportResourceTypeEnum.DATASCREEN.name(), "index");
        for (NewReportConditionDO conditionDO : conditionComponentList) {
            if (StringUtils.isBlank(conditionDO.getUuid()) || StringUtils.isBlank(conditionDO.getConfigUuid())) {
                throw new BusinessException("当前报表筛选器表字段订正失败");
            }
            matchNewUuidValid(conditionDO.getUuid(), conditionDO.getFieldId(), conditionDO.getEnName(), conditionDO.getOriginalName(),
                    datasetColumnList, reportComputeList);
        }

        // 9. 子表 t_report_keyword_component
        List<NewReportKeywordDO> keywordComponentList = keywordService.findByShadeReportId(id, ReportResourceTypeEnum.DATASCREEN.name(), "index");
        for (NewReportKeywordDO keywordDO : keywordComponentList) {
            if (StringUtils.isBlank(keywordDO.getUuid()) || StringUtils.isBlank(keywordDO.getConfigUuid())) {
                throw new BusinessException("当前报表关键字表字段订正失败");
            }
            matchNewUuidValid(keywordDO.getUuid(), keywordDO.getFieldId(), keywordDO.getEnName(), keywordDO.getOriginalName(),
                    datasetColumnList, reportComputeList);
        }

        // 10. 子表 t_report_order_component
        List<NewReportOrderDO> orderComponentList = orderService.findByShadeReportId(id, ReportResourceTypeEnum.DATASCREEN.name(), "index");
        for (NewReportOrderDO orderDO : orderComponentList) {
            if (StringUtils.isBlank(orderDO.getUuid()) || StringUtils.isBlank(orderDO.getConfigUuid())) {
                throw new BusinessException("当前报表排序表字段订正失败");
            }
            matchNewUuidValid(orderDO.getUuid(), orderDO.getFieldId(), orderDO.getEnName(), orderDO.getOriginalName(),
                    datasetColumnList, reportComputeList);
        }

        // 11. 子表 t_report_style_config
        NewReportStyleDO styleDO = styleService.findByShadeReportId(id, ReportResourceTypeEnum.DATASCREEN.name(), "index");
        if (Objects.nonNull(styleDO) && StringUtils.isNoneBlank(styleDO.getTableConfiguration())) {
            TableConfiguration tableConfiguration = JSONUtil.toBean(styleDO.getTableConfiguration(),
                    TableConfiguration.class);
            if (tableConfiguration.getBasicFormat() != null
                    && CollUtil.isNotEmpty(tableConfiguration.getBasicFormat().getCustomColumnWidth())) {
                List<CustomColumnWidth> customColumnWidth = tableConfiguration.getBasicFormat().getCustomColumnWidth();
                for (CustomColumnWidth columnWidth : customColumnWidth) {
                    if (StringUtils.isBlank(columnWidth.getUuid()) || StringUtils.isBlank(columnWidth.getConfigUuid())) {
                        throw new BusinessException("自定义表头宽度订正失败");
                    }

                    if (!uuidConfigSetForWarn.contains(columnWidth.getConfigUuid())) {
                        throw new BusinessException("自定义宽度订正失败" + columnWidth.getUuid());
                    }
                    if (!ReportFieldEnum.MEASURE.getCode().equals(columnWidth.getReportField())) {
                        matchNewUuidValid(columnWidth.getUuid(), null, null, columnWidth.getColumnName(),
                                datasetColumnList, reportComputeList);
                    } else {
                        if (!measureUuidSet.contains(columnWidth.getUuid())) {
                            throw new BusinessException("自定义宽度订正失败" + columnWidth.getUuid());
                        }
                    }
                }
                styleDO.setTableConfiguration(JSONUtil.toJsonStr(tableConfiguration));
            }
        }
    }
}
