package com.bestpay.bigdata.bi.report.bean.warn;

import com.bestpay.bigdata.bi.common.dto.warn.ReportRule;
import com.bestpay.bigdata.bi.report.schedule.ScheduleConfig;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ClassName: ReportWarnVO
 * Package: com.bestpay.bigdata.bi.report.bean
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/8/4 17:02
 * @Version 1.0
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel("报表告警VO")
public class ReportWarnVO{
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "code")
    private String code;

    @ApiModelProperty(value = "报表id")
    private String reportId;

    @ApiModelProperty(value = "告警源类型")
    private String warnSourceType;

    @ApiModelProperty(value = "预警规则 0所有规则 1任意规则")
    private Integer warnRule;

    @ApiModelProperty(value = "告警规则配置")
    private List<ReportRule> reportRuleList;

    @ApiModelProperty(value = "调度配置")
    private ScheduleConfig scheduleConfig;

    @ApiModelProperty(value = "告警配置")
    private WarnConfig warnConfig;
}
