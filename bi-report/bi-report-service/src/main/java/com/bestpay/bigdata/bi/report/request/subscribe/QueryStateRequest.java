package com.bestpay.bigdata.bi.report.request.subscribe;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @ClassName QueryStateRequest
 * @description 查询状态入参
 * @date 2025/5/22
 */
@Data
 @ApiModel(description = "查询状态入参")
public class QueryStateRequest {

  @ApiModelProperty(value = "订阅任务ID")
  @NotNull(message = "订阅任务ID不能为空")
  private String subCode;

   @ApiModelProperty(value = "实例ID")
  @NotNull(message = "实例ID不能为空")
  private String instanceId;
}
