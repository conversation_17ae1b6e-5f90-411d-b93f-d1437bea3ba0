package com.bestpay.bigdata.bi.report.sql.function;

import com.bestpay.bigdata.bi.common.dto.report.function.FunctionInfo;
import com.bestpay.bigdata.bi.report.sql.bean.SqlResult;

import java.util.Optional;

/**
 * ClassName: HighFunctionProcess
 * Package: com.bestpay.bigdata.bi.report.sql.function
 * Description:
 *
 * <AUTHOR>
 * @Create 2023/11/8 14:58
 * @Version 1.0
 */
public interface HighFunctionProcess {

    Optional<TotalFunctionResult> processTotalHighFunction(FunctionInfo functionInfo);

    Optional<LodFunctionResult> processLodHighFunction(FunctionInfo functionInfo);

    void processSqlResult(SqlResult sqlResult, FunctionProcessResult functionProcessResult);
}
