package com.bestpay.bigdata.bi.report.request.report;

import com.bestpay.bigdata.bi.common.dto.dataset.DatasetInfo;
import com.bestpay.bigdata.bi.common.dto.report.QueryReportConditionInfo;
import com.bestpay.bigdata.bi.common.dto.report.ReportSimpleColumn;
import com.bestpay.bigdata.bi.common.dto.report.TableConfiguration;
import com.bestpay.bigdata.bi.common.dto.report.TotalDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.ComputeComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.ConditionComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.ContrastComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.DimensionComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.FilterComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.IndexComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.KeywordComponentPropertyDTO;
import com.bestpay.bigdata.bi.common.dto.report.component.OrderComponentDTO;
import com.bestpay.bigdata.bi.report.bean.report.RollAndDownDTO;
import com.bestpay.bigdata.bi.report.enums.common.DataRequestTypeEnum;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.ToString;

/**
 * 报表查询类
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
public class ReportRequest{

  /**
   * 创建人组织机构代码
   */
  @ApiModelProperty(value = "创建人组织机构代码",required = true)
  private String createdByOrg;

  @ApiModelProperty(value = "展示字段/维度",required = true)
  @NotNull(message = "展示字段不能为空！")
  private List<DimensionComponentPropertyDTO> showColumnList = Lists.newArrayList();

  @ApiModelProperty(value = "指标字段",required = true)
  private List<IndexComponentPropertyDTO> indexColumnList  = Lists.newArrayList();

  @ApiModelProperty(value = "查询控件",required = true)
  @NotNull(message = "查询控件不能为空！")
  private List<ConditionComponentPropertyDTO> conditionList  = Lists.newArrayList();

  @ApiModelProperty(value = "排序控件")
  private List<OrderComponentDTO> orderColumnList  = Lists.newArrayList();

  @ApiModelProperty(value = "查询条件")
  private List<QueryReportConditionInfo> queryConditions  = Lists.newArrayList();

  @ApiModelProperty(value = "过滤条件")
  private List<FilterComponentPropertyDTO> filterColumnList  = Lists.newArrayList();

  @ApiModelProperty(value = "对比字段",required = true)
  private List<ContrastComponentPropertyDTO> contrastColumnList  = Lists.newArrayList();

  @ApiModelProperty(value = "计算字段")
  private List<ComputeComponentPropertyDTO> computeColumnList  = Lists.newArrayList();

  @ApiModelProperty(value = "关键字",required = true)
  @NotNull(message = "关键字不能为空！")
  private List<KeywordComponentPropertyDTO> keywordList = Lists.newArrayList();

  @ApiModelProperty(value = "数据集信息",required = true)
  @NotNull(message = "数据集信息不能为空！")
  private List<DatasetInfo> datasetInfoList = Lists.newArrayList();

  @ApiModelProperty(value = "数据是否包含敏感信息: 0: false 1:true",required = true)
  private Integer fileContainSensitiveInfo;

  @ApiModelProperty(value = "敏感字段")
  private String sensitiveFields;

  @ApiModelProperty(value = "当前页数",required = true)
  private Long pageNum;

  @ApiModelProperty(value = "每页多少条",required = true)
  private Long pageSize;

  // 是否走缓存，按照场景判断，ReportModuleEnum
  private String fromFlag;

  // 是否实时查询
  private Boolean isRealtime;

  // QueryType
  private Integer moduleFlag;

  private String show = DataRequestTypeEnum.DATA.getCode();

  @ApiModelProperty(value = "计算字段参数")
  private List<QueryReportConditionInfo> paramConditions = Lists.newArrayList();


  /** 上卷、下钻 */
  private RollAndDownDTO rollAndDownRequest;

  private List<ReportSimpleColumn> reportStructureList = Lists.newArrayList();

  private Long id;
  @ApiModelProperty(value = "选择组织编码",required = true)
  @NotNull(message = "选择组织编码不能为空！")
  private String orgSelected;

  @ApiModelProperty(value = "归属组织编码",required = true)
  @NotNull(message = "归属组织编码不能为空！")
  private String orgCode;

  @ApiModelProperty(value = "归属目录ID",required = true)
  @NotNull(message = "归属目录ID不能为空！")
  private Long dirId;

  @ApiModelProperty(value = "报表名称",required = true)
  @NotNull(message = "报表名称不能为空！")
  private String reportName;

  @ApiModelProperty(value = "组织权限",required = true)
  private String orgAuth;

  @ApiModelProperty(value = "报表说明",required = true)
  @NotNull(message = "报表说明不能为空！")
  private String reportDesc;

  @ApiModelProperty(value = "数据权限",required = true)
  private String dataAuth;

  @ApiModelProperty(value = "关键字-用户查询",required = true)
  private String queryKeyword;

  private TableConfiguration tableConfigurationObj;

  @ApiModelProperty(value = "责任人（中文名称）",required = true)
  private String ownerNameCh;

  @ApiModelProperty(value = "责任人登录名",required = true)
  private String ownerName;

  @ApiModelProperty(value = "责任人邮箱",required = true)
  private String email;

  @ApiModelProperty(value = "报表类型（0：明细表  1：聚合表 ）",required = true)
  @NotNull(message = "报表类型不能为空！")
  private Integer reportType;

  @ApiModelProperty(value = "状态（0：上线  1：待发布  9：删除）",required = true)
//  @NotNull(message = "状态不能为空！")
  private Integer statusCode;

  @ApiModelProperty(value = "是否开启上卷下钻 0关闭 1开启, 默认值为0")
  private Integer rollupDown;

  @ApiModelProperty(value = "收藏Id")
  private Integer collectionId;

  @ApiModelProperty(value = "图表类型；0-列表；1-柱形图；2-折线图；3-环形图；",required = true)
  private Integer chartType;

  @ApiModelProperty(value = "图表属性",required = true)
  private Object chartFieldObj;

  @ApiModelProperty(value = "是否显示指标总计")
  private TotalDTO showIndexTotalObj;

  @ApiModelProperty(value = "查询总行数")
  private String maxRows;

  private Boolean noCount;

  private Date createdAt;

  private String createdBy;

  private Date updatedAt;

  private String updatedBy;

  /**
   * 是否过滤行级权限   这个字段只针对于/notFilterRowAuth/querySelectValue 这个接口，传值为 false
   */
  private Boolean filterRowAuth;

  /**
   * 是否数据转置，正常情况为列展示，值为false，转置后行展示，值为true
   */
  private Boolean isDataTransposition;
  /**
   * 转置过的维度数据
   */
  private List<DimensionComponentPropertyDTO> transpositionShowColumnList = Lists.newArrayList();
  /**
   * 转置过的对比数据
   */
  private List<ContrastComponentPropertyDTO> transpositionContrastColumnList  = Lists.newArrayList();


  /**
   * 这个字段暂时只有用于堆积图生效
   * 排序类型（0：默认排序，1：堆积柱的指标总和升序排列，2: 堆积柱的指标总和降序排列）
   */
  @ApiModelProperty(value = "排序类型（Null、0：默认排序，1：堆积柱的指标总和升序排列，2: 堆积柱的指标总和降序排列））")
  private Integer orderType;
}
