package com.bestpay.bigdata.bi.report.request.dashboard;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

/**
 * @author:gaodingsong
 * @description:
 * @createTime:2024/4/17 11:00
 * @version:1.0
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "复制仪表盘请求参数")
public class DashBoardCopyRequest extends CommonIdRequest{

    /**
     * 目录ID  复制数据大屏的时候 此字段无值
     */
    @ApiModelProperty(value = "目录ID")
    private Long dirId;

    @ApiModelProperty(value = "仪表盘名称")
    @NotBlank(message = "名称不能为空")
    @Length(min = 1,max = 50,message = "名称长度范围1～50")
    private String dashboardName;
}
