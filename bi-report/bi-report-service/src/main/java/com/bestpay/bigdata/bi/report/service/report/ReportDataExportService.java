package com.bestpay.bigdata.bi.report.service.report;

import com.bestpay.bigdata.bi.common.entity.UserInfo;
import com.bestpay.bigdata.bi.common.enums.FileType;
import com.bestpay.bigdata.bi.database.bean.report.Report;
import com.bestpay.bigdata.bi.report.download.bean.DataFileResult;
import com.bestpay.bigdata.bi.report.download.persist.PersistHelper;
import com.bestpay.bigdata.bi.report.enums.download.FileFormatTypeEnum;
import com.bestpay.bigdata.bi.report.request.report.DownloadApplyRequest;

/**
 * <AUTHOR>
 */
public interface ReportDataExportService {

  DataFileResult generateDataFile(DownloadApplyRequest downloadApplyRequest,
      Report dbreport,
      UserInfo userInfo,
      PersistHelper persistHelper,
      FileType fileType,
      boolean isReportSubscribe,
      FileFormatTypeEnum isFormat);
}
