package com.bestpay.bigdata.bi.report.response.dataset;

import com.bestpay.bigdata.bi.report.bean.dataset.DataSetResourceBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author：Song
 * @Date：2024/12/6 15:01
 * @Desc:
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("数据集血缘资源")
public class DataSetResourceVO extends DataSetResourceBaseDTO {

    @ApiModelProperty("最近一次访问时间")
    private String lastAccessDt;

    @ApiModelProperty("近90天的访问量")
    private String accessCnt;

    @ApiModelProperty("唯一标识  应前端要求，列表数据必须要有唯一标识")
    private String uniqueKey;

    @ApiModelProperty("数据大屏跳转的时候需要")
    private String dataScreenUUID;

    @ApiModelProperty("数据大屏跳转的时候需要")
    private String versionType;

}
